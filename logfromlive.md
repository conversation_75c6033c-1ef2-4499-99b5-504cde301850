[2025-06-24 23:17:08] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-24 23:17:08] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-24 23:17:38] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-24 23:17:38] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-24 23:18:08] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-24 23:18:08] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-24 23:18:08] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-24 23:18:08] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-24 23:18:22] production.INFO: === TEMPLATE UPDATE DEBUG START ===  
[2025-06-24 23:18:22] production.INFO: Template ID: 1  
[2025-06-24 23:18:22] production.INFO: Request Method: POST  
[2025-06-24 23:18:22] production.INFO: Request URL: https://mbf.mybrokerforex.com/admin/notification/template/update/1  
[2025-06-24 23:18:22] production.INFO: User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-06-24 23:18:22] production.INFO: All Request Data: {"_token":"HPAbw1pCZhpPOykLG9SjCSirU4WnVwdHuMWs5fFL","subject":"Your Account has been Credited","email_status":"on","email_body":"Balance Added Successfully\r\n\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            Balance Notification\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Balance Added Successfully\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Your account balance has been updated with a new deposit.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},We are pleased to inform you that {{amount}} {{currency}} has been added to your account.Amount: {{amount}} {{currency}}New Balance: {{new_balance}} {{currency}}Transaction ID: {{transaction_id}}Date: {{transaction_date}}The funds are now available in your account.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,\r\n                            MBFX Team\r\n                            \r\n                                If you have any questions, please contact our support team.\r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            \r\n                                Account Settings |\r\n                                Contact Support |\r\n                                Privacy Policy\r\n                            \r\n                            \r\n                                &copy; 2025 MBFX. All rights reserved.\r\n                            \r\n                            \r\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\r\n                                update your preferences.","original_email_body":"&amp;lt;!DOCTYPE html&amp;gt;\r\n&amp;lt;html lang=&amp;quot;en&amp;quot;&amp;gt;\r\n&amp;lt;head&amp;gt;\r\n    &amp;lt;meta charset=&amp;quot;UTF-8&amp;quot;&amp;gt;\r\n    &amp;lt;meta name=&amp;quot;viewport&amp;quot; content=&amp;quot;width=device-width, initial-scale=1.0&amp;quot;&amp;gt;\r\n    &amp;lt;title&amp;gt;Balance Added Successfully&amp;lt;\/title&amp;gt;\r\n&amp;lt;\/head&amp;gt;\r\n&amp;lt;body style=&amp;quot;margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f4f4f4; line-height: 1.6;&amp;quot;&amp;gt;\r\n    &amp;lt;!-- Full Width Container --&amp;gt;\r\n    &amp;lt;table width=&amp;quot;100%&amp;quot; cellpadding=&amp;quot;0&amp;quot; cellspacing=&amp;quot;0&amp;quot; border=&amp;quot;0&amp;quot; style=&amp;quot;background-color: #f4f4f4;&amp;quot;&amp;gt;\r\n        &amp;lt;tr&amp;gt;\r\n            &amp;lt;td align=&amp;quot;center&amp;quot;&amp;gt;\r\n                &amp;lt;!-- Email Container --&amp;gt;\r\n                &amp;lt;table width=&amp;quot;600&amp;quot; cellpadding=&amp;quot;0&amp;quot; cellspacing=&amp;quot;0&amp;quot; border=&amp;quot;0&amp;quot; style=&amp;quot;background-color: #ffffff; box-shadow: 0 0 10px rgba(0,0,0,0.1);&amp;quot;&amp;gt;\r\n\r\n                    &amp;lt;!-- Header Banner - Full Width --&amp;gt;\r\n                    &amp;lt;tr&amp;gt;\r\n                        &amp;lt;td width=&amp;quot;100%&amp;quot; style=&amp;quot;background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); padding: 20px 0; text-align: center;&amp;quot;&amp;gt;\r\n                            &amp;lt;h1 style=&amp;quot;margin: 0; font-size: 24px; font-weight: bold; color: #ffffff;&amp;quot;&amp;gt;Balance Notification&amp;lt;\/h1&amp;gt;\r\n                        &amp;lt;\/td&amp;gt;\r\n                    &amp;lt;\/tr&amp;gt;\r\n\r\n                    &amp;lt;!-- Logo Section --&amp;gt;\r\n                    &amp;lt;tr&amp;gt;\r\n                        &amp;lt;td style=&amp;quot;background-color: #ffffff; text-align: center; padding: 20px; border-bottom: 2px solid #e9ecef;&amp;quot;&amp;gt;\r\n                            &amp;lt;img src=&amp;quot;https:\/\/mbf.mybrokerforex.com\/assets\/images\/logoIcon\/logo.png&amp;quot; alt=&amp;quot;MBFX&amp;quot; style=&amp;quot;height: 60px; width: auto; display: block; margin: 0 auto;&amp;quot; onerror=&amp;quot;this.style.display=&amp;#039;none&amp;#039;&amp;quot;&amp;gt;\r\n                        &amp;lt;\/td&amp;gt;\r\n                    &amp;lt;\/tr&amp;gt;\r\n\r\n                    &amp;lt;!-- Title Section --&amp;gt;\r\n                    &amp;lt;tr&amp;gt;\r\n                        &amp;lt;td style=&amp;quot;background-color: #ffffff; padding: 30px 40px 20px; text-align: center;&amp;quot;&amp;gt;\r\n                            &amp;lt;h2 style=&amp;quot;margin: 0; color: #dc3545; font-size: 28px; font-weight: bold;&amp;quot;&amp;gt;Balance Added Successfully&amp;lt;\/h2&amp;gt;\r\n                        &amp;lt;\/td&amp;gt;\r\n                    &amp;lt;\/tr&amp;gt;\r\n\r\n                    &amp;lt;!-- Description Section --&amp;gt;\r\n                    &amp;lt;tr&amp;gt;\r\n                        &amp;lt;td style=&amp;quot;background-color: #ffffff; padding: 0 40px 20px; text-align: center;&amp;quot;&amp;gt;\r\n                            &amp;lt;p style=&amp;quot;margin: 0; color: #6c757d; font-size: 16px;&amp;quot;&amp;gt;Your account balance has been updated with a new deposit.&amp;lt;\/p&amp;gt;\r\n                        &amp;lt;\/td&amp;gt;\r\n                    &amp;lt;\/tr&amp;gt;\r\n\r\n                    &amp;lt;!-- Main Content Section --&amp;gt;\r\n                    &amp;lt;tr&amp;gt;\r\n                        &amp;lt;td style=&amp;quot;background-color: #ffffff; padding: 20px 40px; color: #333333;&amp;quot;&amp;gt;\r\n                            &amp;lt;p&amp;gt;Dear {{fullname}},&amp;lt;\/p&amp;gt;&amp;lt;p&amp;gt;We are pleased to inform you that {{amount}} {{currency}} has been added to your account.&amp;lt;\/p&amp;gt;&amp;lt;ul&amp;gt;&amp;lt;li&amp;gt;&amp;lt;strong&amp;gt;Amount:&amp;lt;\/strong&amp;gt; {{amount}} {{currency}}&amp;lt;\/li&amp;gt;&amp;lt;li&amp;gt;&amp;lt;strong&amp;gt;New Balance:&amp;lt;\/strong&amp;gt; {{new_balance}} {{currency}}&amp;lt;\/li&amp;gt;&amp;lt;li&amp;gt;&amp;lt;strong&amp;gt;Transaction ID:&amp;lt;\/strong&amp;gt; {{transaction_id}}&amp;lt;\/li&amp;gt;&amp;lt;li&amp;gt;&amp;lt;strong&amp;gt;Date:&amp;lt;\/strong&amp;gt; {{transaction_date}}&amp;lt;\/li&amp;gt;&amp;lt;\/ul&amp;gt;&amp;lt;p&amp;gt;The funds are now available in your account.&amp;lt;\/p&amp;gt;\r\n                        &amp;lt;\/td&amp;gt;\r\n                    &amp;lt;\/tr&amp;gt;\r\n\r\n                    &amp;lt;!-- Regards Section --&amp;gt;\r\n                    &amp;lt;tr&amp;gt;\r\n                        &amp;lt;td style=&amp;quot;background-color: #ffffff; padding: 30px 40px; border-top: 1px solid #e9ecef;&amp;quot;&amp;gt;\r\n                            &amp;lt;p style=&amp;quot;margin: 0 0 10px 0; font-size: 16px;&amp;quot;&amp;gt;Best regards,&amp;lt;br&amp;gt;\r\n                            &amp;lt;strong&amp;gt;MBFX Team&amp;lt;\/strong&amp;gt;&amp;lt;\/p&amp;gt;\r\n                            &amp;lt;p style=&amp;quot;font-size: 12px; color: #6c757d; margin: 15px 0 0 0;&amp;quot;&amp;gt;\r\n                                If you have any questions, please contact our support team.\r\n                            &amp;lt;\/p&amp;gt;\r\n                        &amp;lt;\/td&amp;gt;\r\n                    &amp;lt;\/tr&amp;gt;\r\n\r\n                    &amp;lt;!-- Footer Section - Full Width --&amp;gt;\r\n                    &amp;lt;tr&amp;gt;\r\n                        &amp;lt;td width=&amp;quot;100%&amp;quot; style=&amp;quot;background: linear-gradient(135deg, #343a40 0%, #23272b 100%); color: #ffffff; padding: 30px 40px; text-align: center;&amp;quot;&amp;gt;\r\n                            &amp;lt;p style=&amp;quot;margin: 0 0 10px 0; font-size: 14px;&amp;quot;&amp;gt;&amp;lt;strong&amp;gt;MBFX&amp;lt;\/strong&amp;gt; - Professional Trading Platform&amp;lt;\/p&amp;gt;\r\n                            &amp;lt;p style=&amp;quot;margin: 0 0 10px 0; font-size: 14px;&amp;quot;&amp;gt;\r\n                                &amp;lt;a href=&amp;quot;{{site_url}}\/user\/profile\/setting&amp;quot; style=&amp;quot;color: #ffffff; text-decoration: none;&amp;quot;&amp;gt;Account Settings&amp;lt;\/a&amp;gt; |\r\n                                &amp;lt;a href=&amp;quot;{{site_url}}\/contact&amp;quot; style=&amp;quot;color: #ffffff; text-decoration: none;&amp;quot;&amp;gt;Contact Support&amp;lt;\/a&amp;gt; |\r\n                                &amp;lt;a href=&amp;quot;{{site_url}}\/policy\/privacy-policy\/99&amp;quot; style=&amp;quot;color: #ffffff; text-decoration: none;&amp;quot;&amp;gt;Privacy Policy&amp;lt;\/a&amp;gt;\r\n                            &amp;lt;\/p&amp;gt;\r\n                            &amp;lt;p style=&amp;quot;margin: 15px 0 0 0; font-size: 14px;&amp;quot;&amp;gt;\r\n                                &copy; 2025 MBFX. All rights reserved.\r\n                            &amp;lt;\/p&amp;gt;\r\n                            &amp;lt;p style=&amp;quot;font-size: 10px; color: #999999; margin: 10px 0 0 0;&amp;quot;&amp;gt;\r\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\r\n                                &amp;lt;a href=&amp;quot;{{site_url}}\/user\/profile\/setting&amp;quot; style=&amp;quot;color: #999999; text-decoration: none;&amp;quot;&amp;gt;update your preferences&amp;lt;\/a&amp;gt;.\r\n                            &amp;lt;\/p&amp;gt;\r\n                        &amp;lt;\/td&amp;gt;\r\n                    &amp;lt;\/tr&amp;gt;\r\n\r\n                &amp;lt;\/table&amp;gt;\r\n            &amp;lt;\/td&amp;gt;\r\n        &amp;lt;\/tr&amp;gt;\r\n    &amp;lt;\/table&amp;gt;\r\n&amp;lt;\/body&amp;gt;\r\n&amp;lt;\/html&amp;gt;","email_body_final":"Balance Added Successfully\r\n\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            Balance Notification\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Balance Added Successfully\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Your account balance has been updated with a new deposit.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},We are pleased to inform you that {{amount}} {{currency}} has been added to your account.Amount: {{amount}} {{currency}}New Balance: {{new_balance}} {{currency}}Transaction ID: {{transaction_id}}Date: {{transaction_date}}The funds are now available in your account.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,\r\n                            MBFX Team\r\n                            \r\n                                If you have any questions, please contact our support team.\r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            \r\n                                Account Settings |\r\n                                Contact Support |\r\n                                Privacy Policy\r\n                            \r\n                            \r\n                                &copy; 2025 MBFX. All rights reserved.\r\n                            \r\n                            \r\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\r\n                                update your preferences.","force_save":"1","sms_body":"{{amount}} {{wallet_currency}}  credited in your account. Your Current Balance {{post_balance}} {{wallet_currency}} . Transaction: #{{trx}}. Admin note is {{remark}}"}  
[2025-06-24 23:18:22] production.INFO: Request Headers: {"content-type":["application\/x-www-form-urlencoded"],"content-length":["23376"],"x-original-url":["\/admin\/notification\/template\/update\/1"],"priority":["u=0, i"],"sec-fetch-dest":["document"],"sec-fetch-user":["?1"],"sec-fetch-mode":["navigate"],"sec-fetch-site":["same-origin"],"upgrade-insecure-requests":["1"],"origin":["https:\/\/mbf.mybrokerforex.com"],"sec-ch-ua-platform":["\"Windows\""],"sec-ch-ua-mobile":["?0"],"sec-ch-ua":["\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not\/A)Brand\";v=\"24\""],"user-agent":["Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36"],"referer":["https:\/\/mbf.mybrokerforex.com\/admin\/notification\/template\/edit\/1"],"host":["mbf.mybrokerforex.com"],"cookie":["XSRF-TOKEN=eyJpdiI6IlpHWDRQTFY1emVOaUMyeW1mSEljUEE9PSIsInZhbHVlIjoiU2VvRUVVZXRSZ3NFL3Q2Y0lzQ0l0RjNhUC9FbnkvbXlXc0lwNWNpWHJ0MmdXd3l4bVdsYWo0eTFxUUlHV0l3UUFhQXNidmtnOVNtcXgrb3lXOEI2NDJtUmpsOENkcGxPa0RSR1k4azcyVXVKVkxRVzhaZzZKVXlzeERiS0NKK2EiLCJtYWMiOiI4N2U0MDFjZGNlZDA4MTgwYzRjMTIyOTJhOTQ1OTVmYmVkZjljNTFiOTNlY2RlZDRlNTBmNDE4YTY0YmQ1YzViIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImdkZ3MyWU9ZZGRJQkU2dzJ5dkx3Rmc9PSIsInZhbHVlIjoiUTc0YTlPZmQ5UnNWZmxGVTZ4c05saWFzSlREZUhDYm5hM3NiZXhCYzhZMUk1b0RuTDhKWmRSTnB1Mi9oWUdXU3JUQmQwMDB0MGNRb05yU0RycXl6UFdqaDhoUkVNcElpSmY3TEV1RFUyamlXb1FuVzR0aG1iL3l5MEJ5L2Vsc1ciLCJtYWMiOiIyMDZmOWZiOWZiZWUwZWE2MWQ0YTkxNzNkYzZiZTcwNzhiZjYzZmFmNWZhNmE5ZjI0MDY2MGQ5OThlNzAxOGMzIiwidGFnIjoiIn0%3D"],"accept-language":["en-US,en;q=0.9"],"accept-encoding":["gzip, deflate, br, zstd"],"accept":["text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7"],"connection":["close"],"cache-control":["max-age=0"]}  
[2025-06-24 23:18:22] production.INFO: ✅ Validation passed  
[2025-06-24 23:18:22] production.INFO: ✅ Template found - Current subject: Your Account has been Credited  
[2025-06-24 23:18:22] production.INFO: ✅ Template found - Current email_body length: 5249  
[2025-06-24 23:18:22] production.INFO: ✅ Subject updated to: Your Account has been Credited  
[2025-06-24 23:18:22] production.INFO: === EMAIL BODY PROCESSING DEBUG ===  
[2025-06-24 23:18:22] production.INFO: email_body_final exists: YES  
[2025-06-24 23:18:22] production.INFO: email_body_final value: HAS_VALUE  
[2025-06-24 23:18:22] production.INFO: email_body_final length: 2276  
[2025-06-24 23:18:22] production.INFO: email_body_final preview: Balance Added Successfully


    
    
        
            
                
                
[2025-06-24 23:18:22] production.INFO: email_body exists: YES  
[2025-06-24 23:18:22] production.INFO: email_body value: HAS_VALUE  
[2025-06-24 23:18:22] production.INFO: email_body length: 2276  
[2025-06-24 23:18:22] production.INFO: email_body preview: Balance Added Successfully


    
    
        
            
                
                
[2025-06-24 23:18:22] production.INFO: original_email_body exists: YES  
[2025-06-24 23:18:22] production.INFO: original_email_body length: 7751  
[2025-06-24 23:18:22] production.INFO: ✅ Using email_body_final as source  
[2025-06-24 23:18:22] production.INFO: === FINAL PROCESSING RESULTS ===  
[2025-06-24 23:18:22] production.INFO: Original content length: 5249  
[2025-06-24 23:18:22] production.INFO: New email body length: 2276  
[2025-06-24 23:18:22] production.INFO: New email body preview: Balance Added Successfully


    
    
        
            
                
                

                    
                    
                        
                          
[2025-06-24 23:18:22] production.INFO: Content changed: YES  
[2025-06-24 23:18:22] production.INFO: Template 1: Bypassing corruption detection for testing  
[2025-06-24 23:18:22] production.INFO: Template 1: Wrapping content in professional structure  
[2025-06-24 23:18:22] production.INFO: === DATABASE SAVE OPERATION DEBUG ===  
[2025-06-24 23:18:22] production.INFO: Before save - Template email_body length: 5249  
[2025-06-24 23:18:22] production.INFO: Before save - New email_body length: 3175  
[2025-06-24 23:18:22] production.INFO: Before save - Template dirty: []  
[2025-06-24 23:18:22] production.INFO: After setting fields - Template dirty: {"email_body":"<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\n    <title>Email Notification<\/title>\n    <style>\n        body { margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f4f4f4; }\n        .email-container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }\n        .email-content { padding: 20px; }\n        @media only screen and (max-width: 600px) {\n            .email-container { width: 100% !important; }\n            .email-content { padding: 15px !important; }\n        }\n    <\/style>\n<\/head>\n<body style=\"margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f4f4f4;\">\n    <div class=\"email-container\">\n        <div class=\"email-content\">\n            Balance Added Successfully\r\n\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            Balance Notification\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Balance Added Successfully\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Your account balance has been updated with a new deposit.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},We are pleased to inform you that {{amount}} {{currency}} has been added to your account.Amount: {{amount}} {{currency}}New Balance: {{new_balance}} {{currency}}Transaction ID: {{transaction_id}}Date: {{transaction_date}}The funds are now available in your account.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,\r\n                            MBFX Team\r\n                            \r\n                                If you have any questions, please contact our support team.\r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            \r\n                                Account Settings |\r\n                                Contact Support |\r\n                                Privacy Policy\r\n                            \r\n                            \r\n                                &copy; 2025 MBFX. All rights reserved.\r\n                            \r\n                            \r\n                                This email was sent to {{email}}. If you no longer wish to receive these emails,\r\n                                update your preferences.\n        <\/div>\n    <\/div>\n<\/body>\n<\/html>"}  
[2025-06-24 23:18:22] production.INFO: After setting fields - Template email_body length: 3175  
[2025-06-24 23:18:22] production.INFO: Save operation result: SUCCESS  
[2025-06-24 23:18:22] production.INFO: After refresh - Template email_body length: 3175  
[2025-06-24 23:18:22] production.INFO: After refresh - Content matches: YES  
[2025-06-24 23:18:22] production.INFO: ✅ Template 1: Database operation completed  
[2025-06-24 23:18:22] production.INFO: === TEMPLATE UPDATE DEBUG END ===  
[2025-06-24 23:18:39] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-24 23:18:39] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-24 23:18:39] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-24 23:18:39] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-24 23:19:09] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-24 23:19:09] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-24 23:19:09] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-24 23:19:09] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-24 23:19:39] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-24 23:19:39] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-24 23:19:39] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-24 23:19:39] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-24 23:20:09] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-24 23:20:09] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-24 23:20:09] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-24 23:20:09] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
