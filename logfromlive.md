[2025-06-30 16:00:35] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:00:35] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:01:02] production.INFO: === TEMPLATE UPDATE DEBUG START ===  
[2025-06-30 16:01:02] production.INFO: Template ID: 35  
[2025-06-30 16:01:02] production.INFO: Request Method: POST  
[2025-06-30 16:01:02] production.INFO: Request URL: https://mbf.mybrokerforex.com/admin/notification/template/update/35  
[2025-06-30 16:01:02] production.INFO: User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-06-30 16:01:02] production.INFO: All Request Data: {"_token":"WbSY2xiYrIOaKJx5dwGbkjlIuchP7ZKjbaOKfCIn","subject":"Congratulations! Your IB Application has been Approved","email_status":"on","email_body_final":"IB Application Approveded\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Congratulations! Your IB application has been approved.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},Congratulations! Your Introducing Broker (IB) application has been approved.IB Type: {{ib_type}}Referral Code: {{referral_code}}Commission Rate: {{commission_rate}}%Approval Date: {{approval_date}}IB Level: {{ib_level}}You can now start earning commissions by referring new clients to our platform.Access IB Dashboard\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences.","email_body":"IB Application Approveded\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Congratulations! Your IB application has been approved.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},Congratulations! Your Introducing Broker (IB) application has been approved.IB Type: {{ib_type}}Referral Code: {{referral_code}}Commission Rate: {{commission_rate}}%Approval Date: {{approval_date}}IB Level: {{ib_level}}You can now start earning commissions by referring new clients to our platform.Access IB Dashboard\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences.","template_id":"35","sms_body":"Congratulations! Your IB application has been approved. Referral code: {{referral_code}}"}  
[2025-06-30 16:01:02] production.INFO: Request Headers: {"content-type":["application\/x-www-form-urlencoded"],"content-length":["10281"],"x-original-url":["\/admin\/notification\/template\/update\/35"],"priority":["u=0, i"],"sec-fetch-dest":["document"],"sec-fetch-user":["?1"],"sec-fetch-mode":["navigate"],"sec-fetch-site":["same-origin"],"upgrade-insecure-requests":["1"],"origin":["https:\/\/mbf.mybrokerforex.com"],"sec-ch-ua-platform":["\"Windows\""],"sec-ch-ua-mobile":["?0"],"sec-ch-ua":["\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not\/A)Brand\";v=\"24\""],"user-agent":["Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36"],"referer":["https:\/\/mbf.mybrokerforex.com\/admin\/notification\/template\/edit\/35"],"host":["mbf.mybrokerforex.com"],"cookie":["PHPSESSID=d3952eed09dd37b5504eefbe7d77664a; XSRF-TOKEN=eyJpdiI6ImJZVUdtMnROdDhmeDBYKzNrejE0aUE9PSIsInZhbHVlIjoicWdUNEFpYVU4UzQrSzFnck9xN0pWa3RzN0FseUpuZnhWc0xCdjFnU0l2MXAvRVJOWFNHQVk2elc3QmFUT3FPWnlNMXhsN3RXb1FIZzladHpCMG1yNEZGZEtBaURPS0Naa3VlZXpvUVYraXc5Y0I2MlIvRjVXZ0dyQjBZYkpDelkiLCJtYWMiOiJjMDczNzc5ZjQzODVhMTAwOGMzOWFjMjZlZjY5YjcxODBhZDc2ZGQyNWZjZGI3MjI3ZDhkYjBjMmI4N2RmODBhIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImVIMVJNTkN6bWNWL2xNaFN1cklUdXc9PSIsInZhbHVlIjoiSE9aNDBDb1pCUi9tSjBpNHdyVHV2dkJpdnVLS1VXWEVQV0IzZ0txWkJiSkE5ZXZSSUhBM09BeDNVQnlnTXV0a0hZRWNRYXpOWFpqZkZNL2dKbDZIYmhoZGNLcTRiTTVlSXlzY2xyUGQ3ZWR6NVBiakJjc2c4UmVQdEtvNUovU0ciLCJtYWMiOiI4NWVmMDc1MWQyODA3MTA2N2ZlYTYwNTUzOGEzZGRlNzkxOTU1ODE1NzRmY2YxN2VjOTY2YTljMWU1YzJkZTZhIiwidGFnIjoiIn0%3D"],"accept-language":["en-US,en;q=0.9,ms-MY;q=0.8,ms;q=0.7,zh-CN;q=0.6,zh;q=0.5"],"accept-encoding":["gzip, deflate, br, zstd"],"accept":["text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7"],"connection":["close"],"cache-control":["max-age=0"]}  
[2025-06-30 16:01:02] production.INFO: ✅ Validation passed  
[2025-06-30 16:01:02] production.INFO: ✅ Template found - Current subject: Congratulations! Your IB Application has been Approved  
[2025-06-30 16:01:02] production.INFO: ✅ Template found - Current email_body length: 4015  
[2025-06-30 16:01:02] production.INFO: ✅ Subject updated to: Congratulations! Your IB Application has been Approved  
[2025-06-30 16:01:02] production.INFO: === SIMPLIFIED EMAIL BODY PROCESSING ===  
[2025-06-30 16:01:02] production.INFO: Email body source: email_body_final  
[2025-06-30 16:01:02] production.INFO: Email body length: 1328  
[2025-06-30 16:01:02] production.INFO: Email body preview (first 200 chars): IB Application Approveded
                        
                    

                    
                    
                        
                            Congratulations! Your IB   
[2025-06-30 16:01:02] production.INFO: Template 35: Using content directly from editor - NO PROCESSING  
[2025-06-30 16:01:02] production.INFO: === FINAL CONTENT READY FOR SAVE ===  
[2025-06-30 16:01:02] production.INFO: Final email body length: 1328  
[2025-06-30 16:01:02] production.INFO: Final email body preview (first 300 chars): IB Application Approveded
                        
                    

                    
                    
                        
                            Congratulations! Your IB application has been approved.
                        
                    

                    
[2025-06-30 16:01:02] production.INFO: Template 35: Skipping all corruption detection and complex processing to prevent issues  
[2025-06-30 16:01:02] production.INFO: === DATABASE SAVE OPERATION DEBUG ===  
[2025-06-30 16:01:02] production.INFO: Before save - Template email_body length: 4015  
[2025-06-30 16:01:02] production.INFO: Before save - New email_body length: 1328  
[2025-06-30 16:01:02] production.INFO: Before save - Template dirty: []  
[2025-06-30 16:01:02] production.INFO: After setting fields - Template dirty: {"email_body":"IB Application Approveded\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Congratulations! Your IB application has been approved.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},Congratulations! Your Introducing Broker (IB) application has been approved.IB Type: {{ib_type}}Referral Code: {{referral_code}}Commission Rate: {{commission_rate}}%Approval Date: {{approval_date}}IB Level: {{ib_level}}You can now start earning commissions by referring new clients to our platform.Access IB Dashboard\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences."}  
[2025-06-30 16:01:02] production.INFO: After setting fields - Template email_body length: 1328  
[2025-06-30 16:01:02] production.INFO: Save operation result: SUCCESS  
[2025-06-30 16:01:02] production.INFO: After refresh - Template email_body length: 1328  
[2025-06-30 16:01:02] production.INFO: After refresh - Content matches: YES  
[2025-06-30 16:01:02] production.INFO: ✅ Template 35: Database operation completed  
[2025-06-30 16:01:02] production.INFO: === TEMPLATE UPDATE DEBUG END ===  
[2025-06-30 16:01:05] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:01:05] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:01:36] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:01:36] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:02:06] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:02:06] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:02:36] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:02:36] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:03:06] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:03:06] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:04:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:04:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:05:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:05:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:06:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:06:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:07:32] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:07:32] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:08:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:08:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:09:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:09:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:10:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:10:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:11:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:11:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:12:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:12:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:13:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:13:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:14:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:14:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:15:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:15:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:16:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:16:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:17:22] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:17:22] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:17:35] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:17:35] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:18:05] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:18:05] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:19:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:19:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:20:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:20:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:21:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:21:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:22:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:22:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:23:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:23:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:24:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:24:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:25:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:25:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:26:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:26:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:27:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:27:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:27:59] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:27:59] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:28:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:28:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:28:38] production.INFO: === TEMPLATE UPDATE DEBUG START ===  
[2025-06-30 16:28:38] production.INFO: Template ID: 4  
[2025-06-30 16:28:38] production.INFO: Request Method: POST  
[2025-06-30 16:28:38] production.INFO: Request URL: https://mbf.mybrokerforex.com/admin/notification/template/update/4  
[2025-06-30 16:28:38] production.INFO: User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-06-30 16:28:38] production.INFO: All Request Data: {"_token":"vV7deHtUJ1Mhd1EUNa0oiTYXPPF8yVpTohghqZsM","subject":"Your Deposit is Approved","email_status":"on","email_body_final":"Deposit Approveded\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Your deposit request has been approved and processed.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},Great news! Your deposit request has been approved and processed successfully.Approved Amount: {{amount}} {{currency}}Payment Method: {{method_name}}Transaction ID: {{trx}}Approval Date: {{approval_date}}Updated Balance: {{post_balance}} {{currency}}The funds are now available in your account for immediate use.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences.","email_body":"Deposit Approveded\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Your deposit request has been approved and processed.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},Great news! Your deposit request has been approved and processed successfully.Approved Amount: {{amount}} {{currency}}Payment Method: {{method_name}}Transaction ID: {{trx}}Approval Date: {{approval_date}}Updated Balance: {{post_balance}} {{currency}}The funds are now available in your account for immediate use.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences.","template_id":"4","sms_status":"on","sms_body":"Admin Approve Your {{amount}} {{wallet_name}} payment request by {{method_name}} transaction : {{trx}}"}  
[2025-06-30 16:28:38] production.INFO: Request Headers: {"content-type":["application\/x-www-form-urlencoded"],"content-length":["13160"],"x-original-url":["\/admin\/notification\/template\/update\/4"],"priority":["u=0, i"],"sec-fetch-dest":["document"],"sec-fetch-user":["?1"],"sec-fetch-mode":["navigate"],"sec-fetch-site":["same-origin"],"upgrade-insecure-requests":["1"],"origin":["https:\/\/mbf.mybrokerforex.com"],"sec-ch-ua-platform":["\"Windows\""],"sec-ch-ua-mobile":["?0"],"sec-ch-ua":["\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not\/A)Brand\";v=\"24\""],"user-agent":["Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36"],"referer":["https:\/\/mbf.mybrokerforex.com\/admin\/notification\/template\/edit\/4"],"host":["mbf.mybrokerforex.com"],"cookie":["XSRF-TOKEN=eyJpdiI6InRzMlpGM1pjMzNNTUtFZXRvZ1ZybGc9PSIsInZhbHVlIjoiczJVMitCZk1rZVYvcU04YkZxM2F0SXFZdXQ5WEo4dWF0cTIvNytSR0RucFMrZU5udS9IdWV3T1BZbVFweml0TzhCV2tvZC9GaEFaR29Rb0lBOHN5dXFYYmR3TG95cFdrVWVPMVVqdW45a1RTTDR2L1NnQWkrSnJCbnIxZWowK04iLCJtYWMiOiI3ZjA4YjNiNjVmOTA1MDljNzA0OGYzZTBlNmNjMzAxNTIwNmUyNzYzMDM3NDU0MGZjZmM0OWQ0OWY2ZTNhNWUyIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImZKY0hIeS9FWjh6aHlkT1pWVG9od3c9PSIsInZhbHVlIjoiK2lBWlFHQVIxcUhkR0dFREg2N1UzWkhBUHFWZEE4OGxhWGRxdll4Nk16M3F2SnV6VmVSTC85QkNZeUdhOS9aMlEyLzhKS04yTG91a2pQcW5rdit5QmhZWDVPUnlBNGdiditNcjMyVDc0VVp2K1hBa0tQYkwxN3Zmd3Rid09rK3YiLCJtYWMiOiJjNDgzNTBmNGU5NGMyNTExYjVlNDZhOWYyMWE1OTJiYTYyZmE5MGUyY2I0ZjlhZmZlMWEwYzcxYzgxZTg2ZDcyIiwidGFnIjoiIn0%3D"],"accept-language":["en-US,en;q=0.9"],"accept-encoding":["gzip, deflate, br, zstd"],"accept":["text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7"],"connection":["close"],"cache-control":["max-age=0"]}  
[2025-06-30 16:28:38] production.INFO: ✅ Validation passed  
[2025-06-30 16:28:38] production.INFO: ✅ Template found - Current subject: Your Deposit is Approved  
[2025-06-30 16:28:38] production.INFO: ✅ Template found - Current email_body length: 3788  
[2025-06-30 16:28:38] production.INFO: ✅ Subject updated to: Your Deposit is Approved  
[2025-06-30 16:28:38] production.INFO: === SIMPLIFIED EMAIL BODY PROCESSING ===  
[2025-06-30 16:28:38] production.INFO: Email body source: email_body_final  
[2025-06-30 16:28:38] production.INFO: Email body length: 1314  
[2025-06-30 16:28:38] production.INFO: Email body preview (first 200 chars): Deposit Approveded
                        
                    

                    
                    
                        
                            Your deposit request has been ap  
[2025-06-30 16:28:38] production.INFO: Template 4: Using content directly from editor - NO PROCESSING  
[2025-06-30 16:28:38] production.INFO: === FINAL CONTENT READY FOR SAVE ===  
[2025-06-30 16:28:38] production.INFO: Final email body length: 1314  
[2025-06-30 16:28:38] production.INFO: Final email body preview (first 300 chars): Deposit Approveded
                        
                    

                    
                    
                        
                            Your deposit request has been approved and processed.
                        
                    

                    
       
[2025-06-30 16:28:38] production.INFO: Template 4: Skipping all corruption detection and complex processing to prevent issues  
[2025-06-30 16:28:38] production.INFO: === DATABASE SAVE OPERATION DEBUG ===  
[2025-06-30 16:28:38] production.INFO: Before save - Template email_body length: 3788  
[2025-06-30 16:28:38] production.INFO: Before save - New email_body length: 1314  
[2025-06-30 16:28:38] production.INFO: Before save - Template dirty: []  
[2025-06-30 16:28:38] production.INFO: After setting fields - Template dirty: {"email_body":"Deposit Approveded\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Your deposit request has been approved and processed.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},Great news! Your deposit request has been approved and processed successfully.Approved Amount: {{amount}} {{currency}}Payment Method: {{method_name}}Transaction ID: {{trx}}Approval Date: {{approval_date}}Updated Balance: {{post_balance}} {{currency}}The funds are now available in your account for immediate use.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences."}  
[2025-06-30 16:28:38] production.INFO: After setting fields - Template email_body length: 1314  
[2025-06-30 16:28:38] production.INFO: Save operation result: SUCCESS  
[2025-06-30 16:28:38] production.INFO: After refresh - Template email_body length: 1314  
[2025-06-30 16:28:38] production.INFO: After refresh - Content matches: YES  
[2025-06-30 16:28:38] production.INFO: ✅ Template 4: Database operation completed  
[2025-06-30 16:28:38] production.INFO: === TEMPLATE UPDATE DEBUG END ===  
[2025-06-30 16:29:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:29:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:30:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:30:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:31:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:31:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:32:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:32:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:32:44] production.INFO: === TEMPLATE UPDATE DEBUG START ===  
[2025-06-30 16:32:44] production.INFO: Template ID: 4  
[2025-06-30 16:32:44] production.INFO: Request Method: POST  
[2025-06-30 16:32:44] production.INFO: Request URL: https://mbf.mybrokerforex.com/admin/notification/template/update/4  
[2025-06-30 16:32:44] production.INFO: User Agent: Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36  
[2025-06-30 16:32:44] production.INFO: All Request Data: {"_token":"vV7deHtUJ1Mhd1EUNa0oiTYXPPF8yVpTohghqZsM","subject":"Your Deposit is Approved","email_status":"on","email_body_final":"Deposit Approveded\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Your deposit request has been approved and processed.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},Great news! Your deposit request has been approved and processed successfully.Approved Amount: {{amount}} {{currency}}Payment Method: {{method_name}}Transaction ID: {{trx}}Approval Date: {{approval_date}}Updated Balance: {{post_balance}} {{currency}}The funds are now available in your account for immediate use.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences.","email_body":"Deposit Approveded\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Your deposit request has been approved and processed.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},Great news! Your deposit request has been approved and processed successfully.Approved Amount: {{amount}} {{currency}}Payment Method: {{method_name}}Transaction ID: {{trx}}Approval Date: {{approval_date}}Updated Balance: {{post_balance}} {{currency}}The funds are now available in your account for immediate use.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences.","template_id":"4","sms_status":"on","sms_body":"Admin Approve Your {{amount}} {{wallet_name}} payment request by {{method_name}} transaction : {{trx}}"}  
[2025-06-30 16:32:44] production.INFO: Request Headers: {"content-type":["application\/x-www-form-urlencoded"],"content-length":["13160"],"x-original-url":["\/admin\/notification\/template\/update\/4"],"priority":["u=0, i"],"sec-fetch-dest":["document"],"sec-fetch-user":["?1"],"sec-fetch-mode":["navigate"],"sec-fetch-site":["same-origin"],"upgrade-insecure-requests":["1"],"origin":["https:\/\/mbf.mybrokerforex.com"],"sec-ch-ua-platform":["\"Android\""],"sec-ch-ua-mobile":["?1"],"sec-ch-ua":["\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not\/A)Brand\";v=\"24\""],"user-agent":["Mozilla\/5.0 (Linux; Android 6.0; Nexus 5 Build\/MRA58N) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Mobile Safari\/537.36"],"referer":["https:\/\/mbf.mybrokerforex.com\/admin\/notification\/template\/edit\/4"],"host":["mbf.mybrokerforex.com"],"cookie":["XSRF-TOKEN=eyJpdiI6Iktzc2sxbHlWaWh6UjJ4L1Q2MlpobXc9PSIsInZhbHVlIjoiMVNRbHd0cmdOSDVWbDBlSnBNdzJRaUhVamd4TlMrR2hEaHhoNFlQNXlvU01NRTlkejAwREp4dUs5WlpvYlpWekJlc3BuZ3o3V0RHbEpNNmplWmN2aHJLZUpvcWJuVmRuMmFNQVpiVzRLNGdaRHRSSmE1SklyZWNyZUxhV004bEUiLCJtYWMiOiJkMWEzYWM0YWE5ZTAxNDQ4ZWJhMGZkNDYwNGQwZjc1ODRmM2EzYjc5YjVjNTA1N2EzNTA5OTIwNTMwOTYxMzkxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InZBc3dlVmRQUmV5RDRrQWR3NGNSREE9PSIsInZhbHVlIjoiRWRrbUUrMDUwWnU5Q1ZOSkpEMmI4WnlSeHVzeDFsSHBJY0V1ZDNiUytGRXFrMnA1NmNGdlNSRlRiKzRIRTgvekp6K2pMc2V1YkRlRDBQQmg4SlBWR0ZteGFQVXBWcEZyMWZPdUxDSHUzbnp5V0hJWDlQYXM0WlN6VUhOckt5Q28iLCJtYWMiOiIyMmIwN2JkYTllMGMxZjNkNjUyZWNjMTU2MGM4YjVhZmI4ODVkYTc2ODU0MmMzYTgzMDJlYWIwODQ4NDYzNTExIiwidGFnIjoiIn0%3D"],"accept-language":["en-US,en;q=0.9"],"accept-encoding":["gzip, deflate, br, zstd"],"accept":["text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7"],"pragma":["no-cache"],"connection":["close"],"cache-control":["no-cache"]}  
[2025-06-30 16:32:44] production.INFO: ✅ Validation passed  
[2025-06-30 16:32:44] production.INFO: ✅ Template found - Current subject: Your Deposit is Approved  
[2025-06-30 16:32:44] production.INFO: ✅ Template found - Current email_body length: 3788  
[2025-06-30 16:32:44] production.INFO: ✅ Subject updated to: Your Deposit is Approved  
[2025-06-30 16:32:44] production.INFO: === SIMPLIFIED EMAIL BODY PROCESSING ===  
[2025-06-30 16:32:44] production.INFO: Email body source: email_body_final  
[2025-06-30 16:32:44] production.INFO: Email body length: 1314  
[2025-06-30 16:32:44] production.INFO: Email body preview (first 200 chars): Deposit Approveded
                        
                    

                    
                    
                        
                            Your deposit request has been ap  
[2025-06-30 16:32:44] production.INFO: Template 4: Using content directly from editor - NO PROCESSING  
[2025-06-30 16:32:44] production.INFO: === FINAL CONTENT READY FOR SAVE ===  
[2025-06-30 16:32:44] production.INFO: Final email body length: 1314  
[2025-06-30 16:32:44] production.INFO: Final email body preview (first 300 chars): Deposit Approveded
                        
                    

                    
                    
                        
                            Your deposit request has been approved and processed.
                        
                    

                    
       
[2025-06-30 16:32:44] production.INFO: Template 4: Skipping all corruption detection and complex processing to prevent issues  
[2025-06-30 16:32:44] production.INFO: === DATABASE SAVE OPERATION DEBUG ===  
[2025-06-30 16:32:44] production.INFO: Before save - Template email_body length: 3788  
[2025-06-30 16:32:44] production.INFO: Before save - New email_body length: 1314  
[2025-06-30 16:32:44] production.INFO: Before save - Template dirty: []  
[2025-06-30 16:32:44] production.INFO: After setting fields - Template dirty: {"email_body":"Deposit Approveded\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Your deposit request has been approved and processed.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},Great news! Your deposit request has been approved and processed successfully.Approved Amount: {{amount}} {{currency}}Payment Method: {{method_name}}Transaction ID: {{trx}}Approval Date: {{approval_date}}Updated Balance: {{post_balance}} {{currency}}The funds are now available in your account for immediate use.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences."}  
[2025-06-30 16:32:44] production.INFO: After setting fields - Template email_body length: 1314  
[2025-06-30 16:32:44] production.INFO: Save operation result: SUCCESS  
[2025-06-30 16:32:44] production.INFO: After refresh - Template email_body length: 1314  
[2025-06-30 16:32:44] production.INFO: After refresh - Content matches: YES  
[2025-06-30 16:32:44] production.INFO: ✅ Template 4: Database operation completed  
[2025-06-30 16:32:44] production.INFO: === TEMPLATE UPDATE DEBUG END ===  
[2025-06-30 16:33:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:33:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:34:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:34:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:35:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:35:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:36:30] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:36:30] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:37:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:37:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:38:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:38:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:39:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:39:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:40:21] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:40:21] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:40:35] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:40:35] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:41:05] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:41:05] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:42:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:42:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:43:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:43:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:44:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:44:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:44:45] production.INFO: === TEMPLATE UPDATE DEBUG START ===  
[2025-06-30 16:44:45] production.INFO: Template ID: 2  
[2025-06-30 16:44:45] production.INFO: Request Method: POST  
[2025-06-30 16:44:45] production.INFO: Request URL: https://mbf.mybrokerforex.com/admin/notification/template/update/2  
[2025-06-30 16:44:45] production.INFO: User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-06-30 16:44:45] production.INFO: All Request Data: {"_token":"WbSY2xiYrIOaKJx5dwGbkjlIuchP7ZKjbaOKfCIn","subject":"Your Account has been Debited","email_status":"on","email_body_final":"Balance Deducteded\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            A deduction has been made from your account balance.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},A deduction has been made from your account balance as requested.Amount Deducted: {{amount}} {{currency}}Remaining Balance: {{new_balance}} {{currency}}Transaction ID: {{transaction_id}}Date: {{transaction_date}}If you have any questions about this transaction, please contact our support team.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences.","email_body":"Balance Deducteded\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            A deduction has been made from your account balance.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},A deduction has been made from your account balance as requested.Amount Deducted: {{amount}} {{currency}}Remaining Balance: {{new_balance}} {{currency}}Transaction ID: {{transaction_id}}Date: {{transaction_date}}If you have any questions about this transaction, please contact our support team.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences.","template_id":"2","sms_status":"on","sms_body":"{{amount}} {{wallet_currency}}  debited from your account. Your Current Balance {{post_balance}} {{wallet_currency}} . Transaction: #{{trx}}. Admin Note is {{remark}}"}  
[2025-06-30 16:44:45] production.INFO: Request Headers: {"content-type":["application\/x-www-form-urlencoded"],"content-length":["13097"],"x-original-url":["\/admin\/notification\/template\/update\/2"],"priority":["u=0, i"],"sec-fetch-dest":["document"],"sec-fetch-user":["?1"],"sec-fetch-mode":["navigate"],"sec-fetch-site":["same-origin"],"upgrade-insecure-requests":["1"],"origin":["https:\/\/mbf.mybrokerforex.com"],"sec-ch-ua-platform":["\"Windows\""],"sec-ch-ua-mobile":["?0"],"sec-ch-ua":["\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not\/A)Brand\";v=\"24\""],"user-agent":["Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36"],"referer":["https:\/\/mbf.mybrokerforex.com\/admin\/notification\/template\/edit\/2"],"host":["mbf.mybrokerforex.com"],"cookie":["PHPSESSID=d3952eed09dd37b5504eefbe7d77664a; XSRF-TOKEN=eyJpdiI6InIyTWROY0NIVGlnY2pNaDcyVm8rc0E9PSIsInZhbHVlIjoienByVm85UmhFSVB6cDdtSWlTWnFiNU4wdW9ORDY0T3JJWXk1YjE1SFppOXpFV0ZPeEtrOEV4UzIyVDdYcThLNDVxV1B2eW55ZGcySlhwU2duM0FNeW1TSFI3OW5tcFNaR2wwOW0vakJxNUZVUHdoUDdRVkNYditsVjBLbmwwVWQiLCJtYWMiOiJjYjEzOThkMzE3MjI0Y2ZhZTQyY2EyYjRkM2ExOTBjM2FmNGFlOTQyYWZkMTA1YzRjNDE4NzNlNGMzODFiNjRlIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjRxaWV6TjVhbXVYRWR2ZWg1cEE3SFE9PSIsInZhbHVlIjoiRGZENEFDNEJCSU1uUlgrOUF3RFgrTFZ4R0tDUDgvL0RuU2kzYXBhM1VnbndyNDIrMzBuOVh6em5XS2gzMXIxNnNndVlSczZXT2pLbStVeVRpK3V6OVBIQlNyWUtjbVVtZ0tWME93aXU2bllmU0FoQ0pUNnN4V2t6eTRZOWN2dnEiLCJtYWMiOiJiYTRlMDI4NGUzODQ2YTgxMWI1YjdhZGViOTZiNzMyZGEwZjRhODNlOGRlM2M3MjY5NTZmYWU4MjAyZDc4NGNhIiwidGFnIjoiIn0%3D"],"accept-language":["en-US,en;q=0.9,ms-MY;q=0.8,ms;q=0.7,zh-CN;q=0.6,zh;q=0.5"],"accept-encoding":["gzip, deflate, br, zstd"],"accept":["text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7"],"connection":["close"],"cache-control":["max-age=0"]}  
[2025-06-30 16:44:45] production.INFO: ✅ Validation passed  
[2025-06-30 16:44:45] production.INFO: ✅ Template found - Current subject: Your Account has been Debited  
[2025-06-30 16:44:45] production.INFO: ✅ Template found - Current email_body length: 3743  
[2025-06-30 16:44:45] production.INFO: ✅ Subject updated to: Your Account has been Debited  
[2025-06-30 16:44:45] production.INFO: === SIMPLIFIED EMAIL BODY PROCESSING ===  
[2025-06-30 16:44:45] production.INFO: Email body source: email_body_final  
[2025-06-30 16:44:45] production.INFO: Email body length: 1295  
[2025-06-30 16:44:45] production.INFO: Email body preview (first 200 chars): Balance Deducteded
                        
                    

                    
                    
                        
                            A deduction has been made from y  
[2025-06-30 16:44:45] production.INFO: Template 2: Using content directly from editor - NO PROCESSING  
[2025-06-30 16:44:45] production.INFO: === FINAL CONTENT READY FOR SAVE ===  
[2025-06-30 16:44:45] production.INFO: Final email body length: 1295  
[2025-06-30 16:44:45] production.INFO: Final email body preview (first 300 chars): Balance Deducteded
                        
                    

                    
                    
                        
                            A deduction has been made from your account balance.
                        
                    

                    
        
[2025-06-30 16:44:45] production.INFO: Template 2: Skipping all corruption detection and complex processing to prevent issues  
[2025-06-30 16:44:45] production.INFO: === DATABASE SAVE OPERATION DEBUG ===  
[2025-06-30 16:44:45] production.INFO: Before save - Template email_body length: 3743  
[2025-06-30 16:44:45] production.INFO: Before save - New email_body length: 1295  
[2025-06-30 16:44:45] production.INFO: Before save - Template dirty: []  
[2025-06-30 16:44:45] production.INFO: After setting fields - Template dirty: {"email_body":"Balance Deducteded\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            A deduction has been made from your account balance.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},A deduction has been made from your account balance as requested.Amount Deducted: {{amount}} {{currency}}Remaining Balance: {{new_balance}} {{currency}}Transaction ID: {{transaction_id}}Date: {{transaction_date}}If you have any questions about this transaction, please contact our support team.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences."}  
[2025-06-30 16:44:45] production.INFO: After setting fields - Template email_body length: 1295  
[2025-06-30 16:44:45] production.INFO: Save operation result: SUCCESS  
[2025-06-30 16:44:45] production.INFO: After refresh - Template email_body length: 1295  
[2025-06-30 16:44:45] production.INFO: After refresh - Content matches: YES  
[2025-06-30 16:44:45] production.INFO: ✅ Template 2: Database operation completed  
[2025-06-30 16:44:45] production.INFO: === TEMPLATE UPDATE DEBUG END ===  
[2025-06-30 16:45:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:45:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:46:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:46:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:47:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:47:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:48:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:48:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:49:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:49:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:50:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:50:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:51:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:51:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:52:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:52:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:53:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:53:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:54:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:54:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:55:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:55:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:56:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:56:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:57:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:57:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:58:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:58:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 16:59:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 16:59:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:00:30] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:00:30] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:01:26] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:01:26] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:01:36] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:01:36] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:01:54] production.INFO: === TEMPLATE UPDATE DEBUG START ===  
[2025-06-30 17:01:54] production.INFO: Template ID: 31  
[2025-06-30 17:01:54] production.INFO: Request Method: POST  
[2025-06-30 17:01:54] production.INFO: Request URL: https://mbf.mybrokerforex.com/admin/notification/template/update/31  
[2025-06-30 17:01:54] production.INFO: User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-06-30 17:01:54] production.INFO: All Request Data: {"_token":"WbSY2xiYrIOaKJx5dwGbkjlIuchP7ZKjbaOKfCIn","subject":"New IB Application Submitted - {{username}}","email_status":"on","email_body_final":"Account Notifications\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            This is an important notification regarding your account.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},This is an important notification regarding your account.{{message}}\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences.","email_body":"Account Notifications\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            This is an important notification regarding your account.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},This is an important notification regarding your account.{{message}}\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences.","template_id":"31","sms_body":"New IB application from {{username}}. Please review in admin panel."}  
[2025-06-30 17:01:54] production.INFO: Request Headers: {"content-type":["application\/x-www-form-urlencoded"],"content-length":["12021"],"x-original-url":["\/admin\/notification\/template\/update\/31"],"priority":["u=0, i"],"sec-fetch-dest":["document"],"sec-fetch-user":["?1"],"sec-fetch-mode":["navigate"],"sec-fetch-site":["same-origin"],"upgrade-insecure-requests":["1"],"origin":["https:\/\/mbf.mybrokerforex.com"],"sec-ch-ua-platform":["\"Windows\""],"sec-ch-ua-mobile":["?0"],"sec-ch-ua":["\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not\/A)Brand\";v=\"24\""],"user-agent":["Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36"],"referer":["https:\/\/mbf.mybrokerforex.com\/admin\/notification\/template\/edit\/31"],"host":["mbf.mybrokerforex.com"],"cookie":["PHPSESSID=d3952eed09dd37b5504eefbe7d77664a; XSRF-TOKEN=eyJpdiI6ImNoUStUYXExZ0trZ0g2YmdGZ3F2MVE9PSIsInZhbHVlIjoiOTBtbzhkTUpoVjZJeG9xUzNLaEZ0dDRGck1Sa2k2ekRNZ3NwaXZMRDRjaTYxRFlzRStzTWxyRmZ6STVyVTVOVzZpU2hkSVd1Ukx2eGI0QXFaaVR2ek5WV3lGQ1o0TFpCWWtINERmUk5pQVJhZmRiN0ZKVzkvNUY5bW1RM2J0WUwiLCJtYWMiOiJlNmFlMmQwNjU4NTMwOWQ0ODg2YzAzYmVkMWE3NDlkMTc4NDVhYWFkOWJmOGFjZDdlYjEwMmY0ODZlZjFlMjJmIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InMxbkM2K0tDbm11Sy84YUhRdi9ML0E9PSIsInZhbHVlIjoiZVNSc0hMWVlDTE43QVpWbjlYanJLRUY4NlpqU1NpQm5nb2dVVVJRc0VRODArNWpkMEFPYWZ6TjBLdTUva00zamFjR1psQk5nVWtudG0zaDFLWFdTSW9lZGxEMVVlWXl4QVhtZFk2ODByc0xPeG1BT1hKbmhsU3I5SW1sQ1pFcnciLCJtYWMiOiI5YTM0ZjM4N2FmMWQ4YTU0YjZiNWJjOGI5YzUxYjJjY2Y2NjAyMTAwZTk1MmE5NjE0NWRmYjhiZTY1ODJhNTI0IiwidGFnIjoiIn0%3D"],"accept-language":["en-US,en;q=0.9,ms-MY;q=0.8,ms;q=0.7,zh-CN;q=0.6,zh;q=0.5"],"accept-encoding":["gzip, deflate, br, zstd"],"accept":["text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7"],"connection":["close"],"cache-control":["max-age=0"]}  
[2025-06-30 17:01:54] production.INFO: ✅ Validation passed  
[2025-06-30 17:01:54] production.INFO: ✅ Template found - Current subject: New IB Application Submitted - {{username}}  
[2025-06-30 17:01:54] production.INFO: ✅ Template found - Current email_body length: 3417  
[2025-06-30 17:01:54] production.INFO: ✅ Subject updated to: New IB Application Submitted - {{username}}  
[2025-06-30 17:01:54] production.INFO: === SIMPLIFIED EMAIL BODY PROCESSING ===  
[2025-06-30 17:01:54] production.INFO: Email body source: email_body_final  
[2025-06-30 17:01:54] production.INFO: Email body length: 1077  
[2025-06-30 17:01:54] production.INFO: Email body preview (first 200 chars): Account Notifications
                        
                    

                    
                    
                        
                            This is an important notifica  
[2025-06-30 17:01:54] production.INFO: Template 31: Using content directly from editor - NO PROCESSING  
[2025-06-30 17:01:54] production.INFO: === FINAL CONTENT READY FOR SAVE ===  
[2025-06-30 17:01:54] production.INFO: Final email body length: 1077  
[2025-06-30 17:01:54] production.INFO: Final email body preview (first 300 chars): Account Notifications
                        
                    

                    
                    
                        
                            This is an important notification regarding your account.
                        
                    

                      
[2025-06-30 17:01:54] production.INFO: Template 31: Skipping all corruption detection and complex processing to prevent issues  
[2025-06-30 17:01:54] production.INFO: === DATABASE SAVE OPERATION DEBUG ===  
[2025-06-30 17:01:54] production.INFO: Before save - Template email_body length: 3417  
[2025-06-30 17:01:54] production.INFO: Before save - New email_body length: 1077  
[2025-06-30 17:01:54] production.INFO: Before save - Template dirty: []  
[2025-06-30 17:01:54] production.INFO: After setting fields - Template dirty: {"email_body":"Account Notifications\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            This is an important notification regarding your account.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},This is an important notification regarding your account.{{message}}\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences."}  
[2025-06-30 17:01:54] production.INFO: After setting fields - Template email_body length: 1077  
[2025-06-30 17:01:54] production.INFO: Save operation result: SUCCESS  
[2025-06-30 17:01:54] production.INFO: After refresh - Template email_body length: 1077  
[2025-06-30 17:01:54] production.INFO: After refresh - Content matches: YES  
[2025-06-30 17:01:54] production.INFO: ✅ Template 31: Database operation completed  
[2025-06-30 17:01:54] production.INFO: === TEMPLATE UPDATE DEBUG END ===  
[2025-06-30 17:02:05] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:02:05] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:03:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:03:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:04:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:04:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:05:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:05:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:06:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:06:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:07:30] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:07:30] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:08:22] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:08:22] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:08:35] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:08:35] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:08:44] production.INFO: === TEMPLATE UPDATE DEBUG START ===  
[2025-06-30 17:08:44] production.INFO: Template ID: 5  
[2025-06-30 17:08:44] production.INFO: Request Method: POST  
[2025-06-30 17:08:44] production.INFO: Request URL: https://mbf.mybrokerforex.com/admin/notification/template/update/5  
[2025-06-30 17:08:44] production.INFO: User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-06-30 17:08:44] production.INFO: All Request Data: {"_token":"WbSY2xiYrIOaKJx5dwGbkjlIuchP7ZKjbaOKfCIn","subject":"Your Deposit Request is Rejected","email_status":"on","email_body_final":"Deposit Rejected\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Deposit Rejecteded\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            We need to inform you about your recent deposit request.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},We regret to inform you that your deposit request could not be processed at this time.Requested Amount: {{amount}} {{currency}}Payment Method: {{method_name}}Transaction ID: {{trx}}Rejection Reason: {{rejection_reason}}Review Date: {{review_date}}Please contact our support team for assistance or try submitting a new deposit request.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences.","email_body":"Deposit Rejected\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Deposit Rejecteded\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            We need to inform you about your recent deposit request.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},We regret to inform you that your deposit request could not be processed at this time.Requested Amount: {{amount}} {{currency}}Payment Method: {{method_name}}Transaction ID: {{trx}}Rejection Reason: {{rejection_reason}}Review Date: {{review_date}}Please contact our support team for assistance or try submitting a new deposit request.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences.","template_id":"5","sms_status":"on","sms_body":"Admin Rejected Your {{amount}} {{wallet_name}} payment request by {{method_name}}\r\n\r\n{{rejection_message}}"}  
[2025-06-30 17:08:44] production.INFO: Request Headers: {"content-type":["application\/x-www-form-urlencoded"],"content-length":["13610"],"x-original-url":["\/admin\/notification\/template\/update\/5"],"priority":["u=0, i"],"sec-fetch-dest":["document"],"sec-fetch-user":["?1"],"sec-fetch-mode":["navigate"],"sec-fetch-site":["same-origin"],"upgrade-insecure-requests":["1"],"origin":["https:\/\/mbf.mybrokerforex.com"],"sec-ch-ua-platform":["\"Windows\""],"sec-ch-ua-mobile":["?0"],"sec-ch-ua":["\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not\/A)Brand\";v=\"24\""],"user-agent":["Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36"],"referer":["https:\/\/mbf.mybrokerforex.com\/admin\/notification\/template\/edit\/5"],"host":["mbf.mybrokerforex.com"],"cookie":["PHPSESSID=d3952eed09dd37b5504eefbe7d77664a; XSRF-TOKEN=eyJpdiI6IjRwQ3hYZ3FMWHQwcFY3c1R2MWVqNkE9PSIsInZhbHVlIjoiUW9FZkltUXNmYkQ0cys5UXo3WktvcEFFVlhKQUNTWi9iTGI4TnhFVGZFY2hpc1h1bmNKczM0bjVERUlqKy9CN1BWOUd5UE5GUk91cWRmQ05XanRTQXNqWXdTVXp5VGFkSVdlMm94ZVhhelJxa2ZGOUx4Y0V2RXpnMXFMUm1RNGMiLCJtYWMiOiI5MWU4OTg1YWVlZWNlYjQyOGE4ZGI1MGU4NjViNjNmOTgzODcwZTE1ZDI1MDYxNGM2N2U5MDc3YTYwMTNmMjk1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ik9WZysyQ1pIaUJ1WTdSVkh6L3hKc0E9PSIsInZhbHVlIjoiQ2NnMzBGeXBRVkFBTFl5UVNkbWg5WFlRQnptVERMT1RFRnpURDBOcVF1TEVyUG1RU0tQVmp5Z3JpSkV5Q0tIaTQ5UUVhYzNvRDRKQnFjTldyMS9TWXV1VUxjd3hXZlloRFZsdlF6SlMzd3dOVk9PS04yVVdNNCtxRlZFWlRuYkUiLCJtYWMiOiI0NDBjNjYxYjU2NzQ4Y2E4ZTVhOTg0NGM3NGIyOTNjOWUwM2NjNTA5MjYwZDFiNmJlYjg1ZGRlNGY0Mjc0NzM4IiwidGFnIjoiIn0%3D"],"accept-language":["en-US,en;q=0.9,ms-MY;q=0.8,ms;q=0.7,zh-CN;q=0.6,zh;q=0.5"],"accept-encoding":["gzip, deflate, br, zstd"],"accept":["text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7"],"connection":["close"],"cache-control":["max-age=0"]}  
[2025-06-30 17:08:44] production.INFO: ✅ Validation passed  
[2025-06-30 17:08:44] production.INFO: ✅ Template found - Current subject: Your Deposit Request is Rejected  
[2025-06-30 17:08:44] production.INFO: ✅ Template found - Current email_body length: 3813  
[2025-06-30 17:08:44] production.INFO: ✅ Subject updated to: Your Deposit Request is Rejected  
[2025-06-30 17:08:44] production.INFO: === SIMPLIFIED EMAIL BODY PROCESSING ===  
[2025-06-30 17:08:44] production.INFO: Email body source: email_body_final  
[2025-06-30 17:08:44] production.INFO: Email body length: 1681  
[2025-06-30 17:08:44] production.INFO: Email body preview (first 200 chars): Deposit Rejected

    
    
        
            
                
                

                    
                    
                        
                            
        
[2025-06-30 17:08:44] production.INFO: Template 5: Using content directly from editor - NO PROCESSING  
[2025-06-30 17:08:44] production.INFO: === FINAL CONTENT READY FOR SAVE ===  
[2025-06-30 17:08:44] production.INFO: Final email body length: 1681  
[2025-06-30 17:08:44] production.INFO: Final email body preview (first 300 chars): Deposit Rejected

    
    
        
            
                
                

                    
                    
                        
                            
                        
                    

                    
                    
              
[2025-06-30 17:08:44] production.INFO: Template 5: Skipping all corruption detection and complex processing to prevent issues  
[2025-06-30 17:08:44] production.INFO: === DATABASE SAVE OPERATION DEBUG ===  
[2025-06-30 17:08:44] production.INFO: Before save - Template email_body length: 3813  
[2025-06-30 17:08:44] production.INFO: Before save - New email_body length: 1681  
[2025-06-30 17:08:44] production.INFO: Before save - Template dirty: []  
[2025-06-30 17:08:44] production.INFO: After setting fields - Template dirty: {"email_body":"Deposit Rejected\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Deposit Rejecteded\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            We need to inform you about your recent deposit request.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},We regret to inform you that your deposit request could not be processed at this time.Requested Amount: {{amount}} {{currency}}Payment Method: {{method_name}}Transaction ID: {{trx}}Rejection Reason: {{rejection_reason}}Review Date: {{review_date}}Please contact our support team for assistance or try submitting a new deposit request.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences."}  
[2025-06-30 17:08:44] production.INFO: After setting fields - Template email_body length: 1681  
[2025-06-30 17:08:44] production.INFO: Save operation result: SUCCESS  
[2025-06-30 17:08:44] production.INFO: After refresh - Template email_body length: 1681  
[2025-06-30 17:08:44] production.INFO: After refresh - Content matches: YES  
[2025-06-30 17:08:44] production.INFO: ✅ Template 5: Database operation completed  
[2025-06-30 17:08:44] production.INFO: === TEMPLATE UPDATE DEBUG END ===  
[2025-06-30 17:09:06] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:09:06] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:10:25] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:10:25] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:10:35] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:10:35] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:10:43] production.INFO: === TEMPLATE UPDATE DEBUG START ===  
[2025-06-30 17:10:43] production.INFO: Template ID: 17  
[2025-06-30 17:10:43] production.INFO: Request Method: POST  
[2025-06-30 17:10:43] production.INFO: Request URL: https://mbf.mybrokerforex.com/admin/notification/template/update/17  
[2025-06-30 17:10:43] production.INFO: User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-06-30 17:10:43] production.INFO: All Request Data: {"_token":"WbSY2xiYrIOaKJx5dwGbkjlIuchP7ZKjbaOKfCIn","subject":"KYC has been rejected","email_status":"on","email_body_final":"KYC Documents Rejected\r\n\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            KYC Documents Rejected\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            We need additional information for your identity verification.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},This is an important notification regarding your account.{{message}}\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences.","email_body":"KYC Documents Rejected\r\n\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            KYC Documents Rejected\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            We need additional information for your identity verification.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},This is an important notification regarding your account.{{message}}\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences.","template_id":"17","sms_body":"."}  
[2025-06-30 17:10:43] production.INFO: Request Headers: {"content-type":["application\/x-www-form-urlencoded"],"content-length":["9365"],"x-original-url":["\/admin\/notification\/template\/update\/17"],"priority":["u=0, i"],"sec-fetch-dest":["document"],"sec-fetch-user":["?1"],"sec-fetch-mode":["navigate"],"sec-fetch-site":["same-origin"],"upgrade-insecure-requests":["1"],"origin":["https:\/\/mbf.mybrokerforex.com"],"sec-ch-ua-platform":["\"Windows\""],"sec-ch-ua-mobile":["?0"],"sec-ch-ua":["\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not\/A)Brand\";v=\"24\""],"user-agent":["Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36"],"referer":["https:\/\/mbf.mybrokerforex.com\/admin\/notification\/template\/edit\/17"],"host":["mbf.mybrokerforex.com"],"cookie":["PHPSESSID=d3952eed09dd37b5504eefbe7d77664a; XSRF-TOKEN=eyJpdiI6Imp4ZHZCeUZQZ2MweUl0QjlLTjhOWmc9PSIsInZhbHVlIjoiSEh3ZitLa2s1NUVrOWN3RU05ZzFFYU5sVlk1ZGtjTmFsenZyL1NUU3lXUjdSUGVTd29od0ZiWjdIaG9xTjhUTzhNbmd4akdTczlWL1pRU0xxeXZHZlhvWmppNXcrQm01eDJpVFNrNnMzc3gxaE1QZERmYURUUlViY0tMcjZQS1MiLCJtYWMiOiJhNWJmYWJjZWI2NzBkMGJmZGFhMTgzZjYzOWUxY2Q5OTY0MWRkOThmZDE3MjgxN2M1MDI1NmM4ODE1YTg3Nzc5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjJ5TktxOVhyWG52RVo0NUhxSVJ2NGc9PSIsInZhbHVlIjoiRTd4TzUxSWdkZmM5dTFhSktXaEYrbHBwME0yOVJLRTVldHpyM3FKYldxYXBuVXoydThaRDd4VHRwclEzaFd0dzFaeEx1RTI5MU51OEhZNVFXcy9GcTdWa044S0gzMkRRUlJCMWVlR0thZ1JJQlYrSDNiaU90VUxtc1o5OGVZYUEiLCJtYWMiOiIxN2I4MjZhMjBlZjNjNDFkZTc3MmU2OWE0NWM5OWRkY2E1YTkzZjljZGFhNDkwYmUzYjYwMjExYzk4ZTE4ZjUyIiwidGFnIjoiIn0%3D"],"accept-language":["en-US,en;q=0.9,ms-MY;q=0.8,ms;q=0.7,zh-CN;q=0.6,zh;q=0.5"],"accept-encoding":["gzip, deflate, br, zstd"],"accept":["text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7"],"connection":["close"],"cache-control":["max-age=0"]}  
[2025-06-30 17:10:43] production.INFO: ✅ Validation passed  
[2025-06-30 17:10:43] production.INFO: ✅ Template found - Current subject: KYC has been rejected  
[2025-06-30 17:10:43] production.INFO: ✅ Template found - Current email_body length: 3426  
[2025-06-30 17:10:43] production.INFO: ✅ Subject updated to: KYC has been rejected  
[2025-06-30 17:10:43] production.INFO: === SIMPLIFIED EMAIL BODY PROCESSING ===  
[2025-06-30 17:10:43] production.INFO: Email body source: email_body_final  
[2025-06-30 17:10:43] production.INFO: Email body length: 1433  
[2025-06-30 17:10:43] production.INFO: Email body preview (first 200 chars): KYC Documents Rejected


    
    
        
            
                
                

                    
                    
                        
                              
[2025-06-30 17:10:43] production.INFO: Template 17: Using content directly from editor - NO PROCESSING  
[2025-06-30 17:10:43] production.INFO: === FINAL CONTENT READY FOR SAVE ===  
[2025-06-30 17:10:43] production.INFO: Final email body length: 1433  
[2025-06-30 17:10:43] production.INFO: Final email body preview (first 300 chars): KYC Documents Rejected


    
    
        
            
                
                

                    
                    
                        
                            
                        
                    

                    
                    
      
[2025-06-30 17:10:43] production.INFO: Template 17: Skipping all corruption detection and complex processing to prevent issues  
[2025-06-30 17:10:43] production.INFO: === DATABASE SAVE OPERATION DEBUG ===  
[2025-06-30 17:10:43] production.INFO: Before save - Template email_body length: 3426  
[2025-06-30 17:10:43] production.INFO: Before save - New email_body length: 1433  
[2025-06-30 17:10:43] production.INFO: Before save - Template dirty: []  
[2025-06-30 17:10:43] production.INFO: After setting fields - Template dirty: {"email_body":"KYC Documents Rejected\r\n\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            KYC Documents Rejected\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            We need additional information for your identity verification.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},This is an important notification regarding your account.{{message}}\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences."}  
[2025-06-30 17:10:43] production.INFO: After setting fields - Template email_body length: 1433  
[2025-06-30 17:10:43] production.INFO: Save operation result: SUCCESS  
[2025-06-30 17:10:43] production.INFO: After refresh - Template email_body length: 1433  
[2025-06-30 17:10:43] production.INFO: After refresh - Content matches: YES  
[2025-06-30 17:10:43] production.INFO: ✅ Template 17: Database operation completed  
[2025-06-30 17:10:43] production.INFO: === TEMPLATE UPDATE DEBUG END ===  
[2025-06-30 17:11:06] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:11:06] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:12:23] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:12:23] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:12:34] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:12:34] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:12:38] production.INFO: === TEMPLATE UPDATE DEBUG START ===  
[2025-06-30 17:12:38] production.INFO: Template ID: 3  
[2025-06-30 17:12:38] production.INFO: Request Method: POST  
[2025-06-30 17:12:38] production.INFO: Request URL: https://mbf.mybrokerforex.com/admin/notification/template/update/3  
[2025-06-30 17:12:38] production.INFO: User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-06-30 17:12:38] production.INFO: All Request Data: {"_token":"WbSY2xiYrIOaKJx5dwGbkjlIuchP7ZKjbaOKfCIn","subject":"Deposit Completed Successfully","email_status":"on","email_body_final":"Deposit Completed\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Deposit Completeded\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Your deposit has been processed and added to your account.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},Your deposit has been successfully processed and added to your account balance.Deposit Amount: {{amount}} {{currency}}Payment Method: {{method_name}}Transaction ID: {{trx}}Processing Date: {{transaction_date}}New Balance: {{post_balance}} {{currency}}You can now use these funds for trading and other platform services.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences.","email_body":"Deposit Completed\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Deposit Completeded\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Your deposit has been processed and added to your account.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},Your deposit has been successfully processed and added to your account balance.Deposit Amount: {{amount}} {{currency}}Payment Method: {{method_name}}Transaction ID: {{trx}}Processing Date: {{transaction_date}}New Balance: {{post_balance}} {{currency}}You can now use these funds for trading and other platform services.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences.","template_id":"3","sms_status":"on","sms_body":"{{amount}} {{method_currency}} Deposit successfully by {{method_name}}"}  
[2025-06-30 17:12:38] production.INFO: Request Headers: {"content-type":["application\/x-www-form-urlencoded"],"content-length":["13550"],"x-original-url":["\/admin\/notification\/template\/update\/3"],"priority":["u=0, i"],"sec-fetch-dest":["document"],"sec-fetch-user":["?1"],"sec-fetch-mode":["navigate"],"sec-fetch-site":["same-origin"],"upgrade-insecure-requests":["1"],"origin":["https:\/\/mbf.mybrokerforex.com"],"sec-ch-ua-platform":["\"Windows\""],"sec-ch-ua-mobile":["?0"],"sec-ch-ua":["\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not\/A)Brand\";v=\"24\""],"user-agent":["Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36"],"referer":["https:\/\/mbf.mybrokerforex.com\/admin\/notification\/template\/edit\/3"],"host":["mbf.mybrokerforex.com"],"cookie":["PHPSESSID=d3952eed09dd37b5504eefbe7d77664a; XSRF-TOKEN=eyJpdiI6IlI3WTZ5R3VrNjEyWWVIUnZBcFUwU2c9PSIsInZhbHVlIjoiZFFaR05ySG5aaExrb0ZvaWl4dUNkTmM3NUp0WGtGeEd1L2l4bno0Z2tDUFpxQlZlTDNVWDNieGZIRU1QOEVGM2djYmFUd2k3c3NvS0ZwTkNYa2VmS0I1T1lSQ0ZVaHg0cHFLakRCbDFOZVErdUErSkpuYW4vL2JPcGZHbmVFOWUiLCJtYWMiOiJjNjdiOTM1NmQ4YTk5NzdmNTY0M2Y0N2U1NDliMzAzNjU1MjY2OTRiM2E4ZGZhMDcwODY4MTAxNDE1OGNiMThiIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IngrWWJ0Mkppdzl0Vjd3bW1QTnNwM2c9PSIsInZhbHVlIjoiKzRJTWhSOXRUS05uQ1MyYTl2NlY4Rk1FYnQ1ejV3Z2ErM2prdFdoYkY4bnNncWpkaFVPVU5yRWc0Y0ZGcjJRckJoUlhFWVQwTUVUeHlDNC9lSmlSQitzQXhpQ1pwMHdxWVBaODgvZVM1UktZMGdkSjZEYU5DSHpFbW1NRFNLVkIiLCJtYWMiOiI2N2QwMWVjMDUwMzEwZDVlNjZmMTlmNzhjZGRjNGVmNjFhNjEwYjljYmQzMjM3MTI5MjUyZWZlM2I4NzljOTdlIiwidGFnIjoiIn0%3D"],"accept-language":["en-US,en;q=0.9,ms-MY;q=0.8,ms;q=0.7,zh-CN;q=0.6,zh;q=0.5"],"accept-encoding":["gzip, deflate, br, zstd"],"accept":["text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7"],"connection":["close"],"cache-control":["max-age=0"]}  
[2025-06-30 17:12:38] production.INFO: ✅ Validation passed  
[2025-06-30 17:12:38] production.INFO: ✅ Template found - Current subject: Deposit Completed Successfully  
[2025-06-30 17:12:38] production.INFO: ✅ Template found - Current email_body length: 3802  
[2025-06-30 17:12:38] production.INFO: ✅ Subject updated to: Deposit Completed Successfully  
[2025-06-30 17:12:38] production.INFO: === SIMPLIFIED EMAIL BODY PROCESSING ===  
[2025-06-30 17:12:38] production.INFO: Email body source: email_body_final  
[2025-06-30 17:12:38] production.INFO: Email body length: 1670  
[2025-06-30 17:12:38] production.INFO: Email body preview (first 200 chars): Deposit Completed

    
    
        
            
                
                

                    
                    
                        
                            
       
[2025-06-30 17:12:38] production.INFO: Template 3: Using content directly from editor - NO PROCESSING  
[2025-06-30 17:12:38] production.INFO: === FINAL CONTENT READY FOR SAVE ===  
[2025-06-30 17:12:38] production.INFO: Final email body length: 1670  
[2025-06-30 17:12:38] production.INFO: Final email body preview (first 300 chars): Deposit Completed

    
    
        
            
                
                

                    
                    
                        
                            
                        
                    

                    
                    
             
[2025-06-30 17:12:38] production.INFO: Template 3: Skipping all corruption detection and complex processing to prevent issues  
[2025-06-30 17:12:38] production.INFO: === DATABASE SAVE OPERATION DEBUG ===  
[2025-06-30 17:12:38] production.INFO: Before save - Template email_body length: 3802  
[2025-06-30 17:12:38] production.INFO: Before save - New email_body length: 1670  
[2025-06-30 17:12:38] production.INFO: Before save - Template dirty: []  
[2025-06-30 17:12:38] production.INFO: After setting fields - Template dirty: {"email_body":"Deposit Completed\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Deposit Completeded\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Your deposit has been processed and added to your account.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},Your deposit has been successfully processed and added to your account balance.Deposit Amount: {{amount}} {{currency}}Payment Method: {{method_name}}Transaction ID: {{trx}}Processing Date: {{transaction_date}}New Balance: {{post_balance}} {{currency}}You can now use these funds for trading and other platform services.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences."}  
[2025-06-30 17:12:38] production.INFO: After setting fields - Template email_body length: 1670  
[2025-06-30 17:12:38] production.INFO: Save operation result: SUCCESS  
[2025-06-30 17:12:38] production.INFO: After refresh - Template email_body length: 1670  
[2025-06-30 17:12:38] production.INFO: After refresh - Content matches: YES  
[2025-06-30 17:12:38] production.INFO: ✅ Template 3: Database operation completed  
[2025-06-30 17:12:38] production.INFO: === TEMPLATE UPDATE DEBUG END ===  
[2025-06-30 17:13:06] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:13:06] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:13:58] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:13:58] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:14:05] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:14:05] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:14:34] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:14:34] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:14:40] production.INFO: === TEMPLATE UPDATE DEBUG START ===  
[2025-06-30 17:14:40] production.INFO: Template ID: 36  
[2025-06-30 17:14:40] production.INFO: Request Method: POST  
[2025-06-30 17:14:40] production.INFO: Request URL: https://mbf.mybrokerforex.com/admin/notification/template/update/36  
[2025-06-30 17:14:40] production.INFO: User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-06-30 17:14:40] production.INFO: All Request Data: {"_token":"WbSY2xiYrIOaKJx5dwGbkjlIuchP7ZKjbaOKfCIn","subject":"IB Application Status Update","email_status":"on","email_body_final":"IB Application Rejected\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            IB Application Rejected\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            We have reviewed your IB application.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},This is an important notification regarding your account.{{message}}\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences.","email_body":"IB Application Rejected\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            IB Application Rejected\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            We have reviewed your IB application.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},This is an important notification regarding your account.{{message}}\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences.","template_id":"36","sms_body":"Your IB application has been rejected. Reason: {{reason}}"}  
[2025-06-30 17:14:40] production.INFO: Request Headers: {"content-type":["application\/x-www-form-urlencoded"],"content-length":["12370"],"x-original-url":["\/admin\/notification\/template\/update\/36"],"priority":["u=0, i"],"sec-fetch-dest":["document"],"sec-fetch-user":["?1"],"sec-fetch-mode":["navigate"],"sec-fetch-site":["same-origin"],"upgrade-insecure-requests":["1"],"origin":["https:\/\/mbf.mybrokerforex.com"],"sec-ch-ua-platform":["\"Windows\""],"sec-ch-ua-mobile":["?0"],"sec-ch-ua":["\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not\/A)Brand\";v=\"24\""],"user-agent":["Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36"],"referer":["https:\/\/mbf.mybrokerforex.com\/admin\/notification\/template\/edit\/36"],"host":["mbf.mybrokerforex.com"],"cookie":["PHPSESSID=d3952eed09dd37b5504eefbe7d77664a; XSRF-TOKEN=eyJpdiI6IkdicEg4L1ZyV1E1NGRjbFZoQURqYXc9PSIsInZhbHVlIjoidktQNklUNm5HbzNNemhTRlhPTGtOZ0tLVTlJWUxxVkpzbW15bkhtWXNvbWJRVkl3bEJWdEUvYVZBMVVyYkIzd3NVOHhRYW4vbTJDMFI2KzBMY2l4WXlvNUNUOUhmd1RTUjJaaHNKd0ZPblY2Zi9lK28yRTU1Zzl1U1VDRTJJNy8iLCJtYWMiOiI1MTdmMGY4MWVlNGZmNjIwZmM4OWQ2YjQ5MjJkOTQ5NGFkYTEyNDA4MzNhMTY0MTZkNTNjZDBmYjM0MjUxYmNjIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjZRQVNFRFRkeFBMZVNXYTNWbUJ3aHc9PSIsInZhbHVlIjoiZnNLNlpPYklGTnFwVzl3SkFENURaMXNJYTRheXdScFhjU3VDQUNYaGdhOU5sejNvTE8vTXRRcG9sRjI5QzFsRTdPc3ZxN2NEY0V2dWd4YUpUcU9kZmY5QWJ4OW9kMXhqNnlvQTRWUHZNNEE1M1VSREVmSWVSRnBLUXJwRjQxdE8iLCJtYWMiOiI5NmYwYzZkN2EwNDYyMDRiNTQ5ZDQwOTUzODQ1YjU4YTI3M2Q3MGYzM2YxY2VhMGY3MmY3YTAzYjUxZTFhNmVhIiwidGFnIjoiIn0%3D"],"accept-language":["en-US,en;q=0.9,ms-MY;q=0.8,ms;q=0.7,zh-CN;q=0.6,zh;q=0.5"],"accept-encoding":["gzip, deflate, br, zstd"],"accept":["text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7"],"connection":["close"],"cache-control":["max-age=0"]}  
[2025-06-30 17:14:40] production.INFO: ✅ Validation passed  
[2025-06-30 17:14:40] production.INFO: ✅ Template found - Current subject: IB Application Status Update  
[2025-06-30 17:14:40] production.INFO: ✅ Template found - Current email_body length: 3403  
[2025-06-30 17:14:40] production.INFO: ✅ Subject updated to: IB Application Status Update  
[2025-06-30 17:14:40] production.INFO: === SIMPLIFIED EMAIL BODY PROCESSING ===  
[2025-06-30 17:14:40] production.INFO: Email body source: email_body_final  
[2025-06-30 17:14:40] production.INFO: Email body length: 1408  
[2025-06-30 17:14:40] production.INFO: Email body preview (first 200 chars): IB Application Rejected

    
    
        
            
                
                

                    
                    
                        
                            
  
[2025-06-30 17:14:40] production.INFO: Template 36: Using content directly from editor - NO PROCESSING  
[2025-06-30 17:14:40] production.INFO: === FINAL CONTENT READY FOR SAVE ===  
[2025-06-30 17:14:40] production.INFO: Final email body length: 1408  
[2025-06-30 17:14:40] production.INFO: Final email body preview (first 300 chars): IB Application Rejected

    
    
        
            
                
                

                    
                    
                        
                            
                        
                    

                    
                    
       
[2025-06-30 17:14:40] production.INFO: Template 36: Skipping all corruption detection and complex processing to prevent issues  
[2025-06-30 17:14:40] production.INFO: === DATABASE SAVE OPERATION DEBUG ===  
[2025-06-30 17:14:40] production.INFO: Before save - Template email_body length: 3403  
[2025-06-30 17:14:40] production.INFO: Before save - New email_body length: 1408  
[2025-06-30 17:14:40] production.INFO: Before save - Template dirty: []  
[2025-06-30 17:14:40] production.INFO: After setting fields - Template dirty: {"email_body":"IB Application Rejected\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            IB Application Rejected\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            We have reviewed your IB application.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},This is an important notification regarding your account.{{message}}\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences."}  
[2025-06-30 17:14:40] production.INFO: After setting fields - Template email_body length: 1408  
[2025-06-30 17:14:40] production.INFO: Save operation result: SUCCESS  
[2025-06-30 17:14:40] production.INFO: After refresh - Template email_body length: 1408  
[2025-06-30 17:14:40] production.INFO: After refresh - Content matches: YES  
[2025-06-30 17:14:40] production.INFO: ✅ Template 36: Database operation completed  
[2025-06-30 17:14:40] production.INFO: === TEMPLATE UPDATE DEBUG END ===  
[2025-06-30 17:15:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:15:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:16:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:16:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:17:30] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:17:30] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:18:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:18:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:19:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:19:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:20:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:20:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:21:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:21:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:22:30] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:22:30] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:23:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:23:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:24:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:24:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:25:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:25:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:26:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:26:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:27:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:27:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:28:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:28:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:29:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:29:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:30:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:30:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:31:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:31:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:32:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:32:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:33:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:33:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:34:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:34:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:35:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:35:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:36:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:36:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:37:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:37:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:38:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:38:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:39:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:39:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:39:37] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:39:37] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:40:06] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:40:06] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:40:35] production.INFO: === TEMPLATE UPDATE DEBUG START ===  
[2025-06-30 17:40:35] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:40:35] production.INFO: Template ID: 36  
[2025-06-30 17:40:35] production.INFO: Request Method: POST  
[2025-06-30 17:40:35] production.INFO: Request URL: https://mbf.mybrokerforex.com/admin/notification/template/update/36  
[2025-06-30 17:40:35] production.INFO: User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-06-30 17:40:35] production.INFO: All Request Data: {"_token":"WbSY2xiYrIOaKJx5dwGbkjlIuchP7ZKjbaOKfCIn","subject":"IB Application Status Update","email_status":"on","email_body_final":"IB Application Rejected\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            IB Application Rejecteded\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            We have reviewed your IB application.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},We have reviewed your IB application and unfortunately cannot approve it at this time.Application Date: {{application_date}}IB Type: {{requested_ib_type}}Review Date: {{review_date}}Rejection Reason: {{rejection_reason}}You may reapply after addressing the mentioned requirements.Thank you for your interest in our IB program.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences.","email_body":"IB Application Rejected\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            IB Application Rejecteded\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            We have reviewed your IB application.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},We have reviewed your IB application and unfortunately cannot approve it at this time.Application Date: {{application_date}}IB Type: {{requested_ib_type}}Review Date: {{review_date}}Rejection Reason: {{rejection_reason}}You may reapply after addressing the mentioned requirements.Thank you for your interest in our IB program.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences.","template_id":"36","sms_body":"Your IB application has been rejected. Reason: {{reason}}"}  
[2025-06-30 17:40:35] production.INFO: Request Headers: {"content-type":["application\/x-www-form-urlencoded"],"content-length":["13586"],"x-original-url":["\/admin\/notification\/template\/update\/36"],"priority":["u=0, i"],"sec-fetch-dest":["document"],"sec-fetch-user":["?1"],"sec-fetch-mode":["navigate"],"sec-fetch-site":["same-origin"],"upgrade-insecure-requests":["1"],"origin":["https:\/\/mbf.mybrokerforex.com"],"sec-ch-ua-platform":["\"Windows\""],"sec-ch-ua-mobile":["?0"],"sec-ch-ua":["\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not\/A)Brand\";v=\"24\""],"user-agent":["Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36"],"referer":["https:\/\/mbf.mybrokerforex.com\/admin\/notification\/template\/edit\/36"],"host":["mbf.mybrokerforex.com"],"cookie":["PHPSESSID=d3952eed09dd37b5504eefbe7d77664a; XSRF-TOKEN=eyJpdiI6IldPMHJwOU9PVnhEWHNaaWZYanBOL1E9PSIsInZhbHVlIjoiSDMxUHVFZ0ZKTmJEQXh6SEgwNXJRK2RRekdxeERnU1ZiNGFMeGpyZThNcUJZMy94RGJsNUZGUVBLYVdpbkwvcDB4M3VOT0Q2V0tKS21uQ3RxTzViMzBLVzdSbHFFTlFCb1JQK3pXZitYQXBaQXhxa25sSUVEV2ZuWDFkbnp5cUUiLCJtYWMiOiJiODczYTI3ZWM5MjI1OWI3MDJkZWFlYWY2OGU1YzM4MGMyMTE1MWU1N2U4YjE4YzBhNTlhZjNiYWJmMWJlNTMzIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjdkUXFKcnZRWjRBQmZ4a0pRUTNRUWc9PSIsInZhbHVlIjoiZDhzV3VNQUwrOUtrdjhTRnZ6MGJZRGt2ZTUrNEJ2RnNrc004RURmZzU0RzNhaXh2a0lZRXFsbGtVcnRRWjZIL0JOOEVrMWk3N3pZN0dLZ1NzbmE5a0FUQ1JEWStsYUp5aGZFU3hjd2tsS2h4U3JjeDBMT1ZmelhoWGRMam8wRzYiLCJtYWMiOiIwZWFhNDU0MTI0NTc1ZmYxOTIxNTUxMzFlNmMwOTQ3ZWUyODc2MTQ5NmM2NzRhNzMzMDdjOGU2ZGQzNzI0YThmIiwidGFnIjoiIn0%3D"],"accept-language":["en-US,en;q=0.9,ms-MY;q=0.8,ms;q=0.7,zh-CN;q=0.6,zh;q=0.5"],"accept-encoding":["gzip, deflate, br, zstd"],"accept":["text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7"],"connection":["close"],"cache-control":["max-age=0"]}  
[2025-06-30 17:40:35] production.INFO: ✅ Validation passed  
[2025-06-30 17:40:35] production.INFO: ✅ Template found - Current subject: IB Application Status Update  
[2025-06-30 17:40:35] production.INFO: ✅ Template found - Current email_body length: 3781  
[2025-06-30 17:40:35] production.INFO: ✅ Subject updated to: IB Application Status Update  
[2025-06-30 17:40:35] production.INFO: === SIMPLIFIED EMAIL BODY PROCESSING ===  
[2025-06-30 17:40:35] production.INFO: Email body source: email_body_final  
[2025-06-30 17:40:35] production.INFO: Email body length: 1668  
[2025-06-30 17:40:35] production.INFO: Email body preview (first 200 chars): IB Application Rejected

    
    
        
            
                
                

                    
                    
                        
                            
  
[2025-06-30 17:40:35] production.INFO: Template 36: Using content directly from editor - NO PROCESSING  
[2025-06-30 17:40:35] production.INFO: === FINAL CONTENT READY FOR SAVE ===  
[2025-06-30 17:40:35] production.INFO: Final email body length: 1668  
[2025-06-30 17:40:35] production.INFO: Final email body preview (first 300 chars): IB Application Rejected

    
    
        
            
                
                

                    
                    
                        
                            
                        
                    

                    
                    
       
[2025-06-30 17:40:35] production.INFO: Template 36: Skipping all corruption detection and complex processing to prevent issues  
[2025-06-30 17:40:35] production.INFO: === DATABASE SAVE OPERATION DEBUG ===  
[2025-06-30 17:40:35] production.INFO: Before save - Template email_body length: 3781  
[2025-06-30 17:40:35] production.INFO: Before save - New email_body length: 1668  
[2025-06-30 17:40:35] production.INFO: Before save - Template dirty: []  
[2025-06-30 17:40:35] production.INFO: After setting fields - Template dirty: {"email_body":"IB Application Rejected\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            IB Application Rejecteded\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            We have reviewed your IB application.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},We have reviewed your IB application and unfortunately cannot approve it at this time.Application Date: {{application_date}}IB Type: {{requested_ib_type}}Review Date: {{review_date}}Rejection Reason: {{rejection_reason}}You may reapply after addressing the mentioned requirements.Thank you for your interest in our IB program.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences."}  
[2025-06-30 17:40:35] production.INFO: After setting fields - Template email_body length: 1668  
[2025-06-30 17:40:35] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:40:35] production.INFO: Save operation result: SUCCESS  
[2025-06-30 17:40:35] production.INFO: After refresh - Template email_body length: 1668  
[2025-06-30 17:40:35] production.INFO: After refresh - Content matches: YES  
[2025-06-30 17:40:35] production.INFO: ✅ Template 36: Database operation completed  
[2025-06-30 17:40:35] production.INFO: === TEMPLATE UPDATE DEBUG END ===  
[2025-06-30 17:41:30] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:41:30] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:42:30] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:42:30] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:43:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:43:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:44:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:44:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:45:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:45:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:46:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:46:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:47:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:47:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:48:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:48:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:49:30] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:49:30] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:49:36] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:49:36] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:50:03] production.INFO: === TEMPLATE UPDATE DEBUG START ===  
[2025-06-30 17:50:03] production.INFO: Template ID: 2  
[2025-06-30 17:50:03] production.INFO: Request Method: POST  
[2025-06-30 17:50:03] production.INFO: Request URL: https://mbf.mybrokerforex.com/admin/notification/template/update/2  
[2025-06-30 17:50:03] production.INFO: User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-06-30 17:50:03] production.INFO: All Request Data: {"_token":"WbSY2xiYrIOaKJx5dwGbkjlIuchP7ZKjbaOKfCIn","subject":"Your Account has been Debited","email_status":"on","email_body_final":"Balance Deducted\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Balance Deductede\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            A deduction has been made from your account balance.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},A deduction has been made from your account balance as requested.Amount Deducted: {{amount}} {{currency}}Remaining Balance: {{new_balance}} {{currency}}Transaction ID: {{transaction_id}}Date: {{transaction_date}}If you have any questions about this transaction, please contact our support team.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences.","email_body":"Balance Deducted\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Balance Deductede\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            A deduction has been made from your account balance.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},A deduction has been made from your account balance as requested.Amount Deducted: {{amount}} {{currency}}Remaining Balance: {{new_balance}} {{currency}}Transaction ID: {{transaction_id}}Date: {{transaction_date}}If you have any questions about this transaction, please contact our support team.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences.","template_id":"2","sms_status":"on","sms_body":"{{amount}} {{wallet_currency}}  debited from your account. Your Current Balance {{post_balance}} {{wallet_currency}} . Transaction: #{{trx}}. Admin Note is {{remark}}"}  
[2025-06-30 17:50:03] production.INFO: Request Headers: {"content-type":["application\/x-www-form-urlencoded"],"content-length":["13497"],"x-original-url":["\/admin\/notification\/template\/update\/2"],"priority":["u=0, i"],"sec-fetch-dest":["document"],"sec-fetch-user":["?1"],"sec-fetch-mode":["navigate"],"sec-fetch-site":["same-origin"],"upgrade-insecure-requests":["1"],"origin":["https:\/\/mbf.mybrokerforex.com"],"sec-ch-ua-platform":["\"Windows\""],"sec-ch-ua-mobile":["?0"],"sec-ch-ua":["\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not\/A)Brand\";v=\"24\""],"user-agent":["Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36"],"referer":["https:\/\/mbf.mybrokerforex.com\/admin\/notification\/template\/edit\/2"],"host":["mbf.mybrokerforex.com"],"cookie":["PHPSESSID=d3952eed09dd37b5504eefbe7d77664a; XSRF-TOKEN=eyJpdiI6IkZVa203Y3dKK2VsVUh3OXU5M2NiUXc9PSIsInZhbHVlIjoiRzFyVEJpV1NoQTBVY2d2QU1VYmFqWEpYL1NDbTh5TzNLZklIdy9PVGU1VUVvOEl2TkVhZlRJR0hxWUV6TG0rUFhKcVU3SUVxL2J5TkpPUW1ZVVdBc3pUZzRaNVhFN253RnFqNUhWQXo3ZXdLbzdDQlM5bGxQcVRmczZEV0hta1kiLCJtYWMiOiI2MTI5MDJjNTNhY2ZmNmRmNDFlM2M0YTViMTcyYTY4MWRiNGQ5NGQ1YTY0Y2I1NWUxNmYxZjYxYWI4ZjIyZGQ4IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImVTaDc5YlhueEI1Tk9CSGlNdThyZVE9PSIsInZhbHVlIjoiV0YzYjNJcHVVN01QVk9JSHNNb1Z6d0cxQmdkYlg5aFo5THVxY1E5K1QwUFlFWkNEbDE0dVdyRG1ETXhYQ3R6aHJobUt2MGRzQ0R3V0ZSclhld20xVnlZZkh0Q29jUjVRUGlmY2hRRy8yTm9LODNZcUdEbk1JbTNWTmM2VmlkZ2IiLCJtYWMiOiI0M2Y0MjZlZWZjM2Y4Y2Y4OGM1ZDUxZmQ4ZWZmNjU0Y2FkYzJhZGYzYTAwY2Y4MDkwYjg4ZTE3ZTM0NzgzMThlIiwidGFnIjoiIn0%3D"],"accept-language":["en-US,en;q=0.9,ms-MY;q=0.8,ms;q=0.7,zh-CN;q=0.6,zh;q=0.5"],"accept-encoding":["gzip, deflate, br, zstd"],"accept":["text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7"],"connection":["close"],"cache-control":["max-age=0"]}  
[2025-06-30 17:50:03] production.INFO: ✅ Validation passed  
[2025-06-30 17:50:03] production.INFO: ✅ Template found - Current subject: Your Account has been Debited  
[2025-06-30 17:50:03] production.INFO: ✅ Template found - Current email_body length: 3743  
[2025-06-30 17:50:03] production.INFO: ✅ Subject updated to: Your Account has been Debited  
[2025-06-30 17:50:03] production.INFO: === SIMPLIFIED EMAIL BODY PROCESSING ===  
[2025-06-30 17:50:03] production.INFO: Email body source: email_body_final  
[2025-06-30 17:50:03] production.INFO: Email body length: 1636  
[2025-06-30 17:50:03] production.INFO: Email body preview (first 200 chars): Balance Deducted

    
    
        
            
                
                

                    
                    
                        
                            
        
[2025-06-30 17:50:03] production.INFO: Template 2: Using content directly from editor - NO PROCESSING  
[2025-06-30 17:50:03] production.INFO: === FINAL CONTENT READY FOR SAVE ===  
[2025-06-30 17:50:03] production.INFO: Final email body length: 1636  
[2025-06-30 17:50:03] production.INFO: Final email body preview (first 300 chars): Balance Deducted

    
    
        
            
                
                

                    
                    
                        
                            
                        
                    

                    
                    
              
[2025-06-30 17:50:03] production.INFO: Template 2: Skipping all corruption detection and complex processing to prevent issues  
[2025-06-30 17:50:03] production.INFO: === DATABASE SAVE OPERATION DEBUG ===  
[2025-06-30 17:50:03] production.INFO: Before save - Template email_body length: 3743  
[2025-06-30 17:50:03] production.INFO: Before save - New email_body length: 1636  
[2025-06-30 17:50:03] production.INFO: Before save - Template dirty: []  
[2025-06-30 17:50:03] production.INFO: After setting fields - Template dirty: {"email_body":"Balance Deducted\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Balance Deductede\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            A deduction has been made from your account balance.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},A deduction has been made from your account balance as requested.Amount Deducted: {{amount}} {{currency}}Remaining Balance: {{new_balance}} {{currency}}Transaction ID: {{transaction_id}}Date: {{transaction_date}}If you have any questions about this transaction, please contact our support team.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences."}  
[2025-06-30 17:50:03] production.INFO: After setting fields - Template email_body length: 1636  
[2025-06-30 17:50:03] production.INFO: Save operation result: SUCCESS  
[2025-06-30 17:50:03] production.INFO: After refresh - Template email_body length: 1636  
[2025-06-30 17:50:03] production.INFO: After refresh - Content matches: YES  
[2025-06-30 17:50:03] production.INFO: ✅ Template 2: Database operation completed  
[2025-06-30 17:50:03] production.INFO: === TEMPLATE UPDATE DEBUG END ===  
[2025-06-30 17:50:04] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:50:04] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:51:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:51:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:52:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:52:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:53:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:53:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:54:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:54:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:55:01] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:55:01] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:55:02] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:55:02] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:55:34] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:55:34] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:56:03] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:56:03] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 17:56:44] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 17:56:44] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:06:38] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:06:38] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:07:04] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:07:04] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:08:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:08:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:09:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:09:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:10:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:10:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:11:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:11:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:12:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:12:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:13:30] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:13:30] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:14:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:14:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:15:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:15:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:16:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:16:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:17:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:17:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:18:00] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:18:00] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:18:02] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:18:03] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:18:06] production.INFO: === TEMPLATE UPDATE DEBUG START ===  
[2025-06-30 18:18:06] production.INFO: Template ID: 2  
[2025-06-30 18:18:06] production.INFO: Request Method: POST  
[2025-06-30 18:18:06] production.INFO: Request URL: https://mbf.mybrokerforex.com/admin/notification/template/update/2  
[2025-06-30 18:18:06] production.INFO: User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-06-30 18:18:06] production.INFO: All Request Data: {"_token":"WbSY2xiYrIOaKJx5dwGbkjlIuchP7ZKjbaOKfCIn","subject":"Your Account has been Debited","email_status":"on","email_body_final":"Balance Deducted\r\n\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Balance Deducteded\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            A deduction has been made from your account balance.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},A deduction has been made from your account balance as requested.Amount Deducted: {{amount}} {{currency}}Remaining Balance: {{new_balance}} {{currency}}Transaction ID: {{transaction_id}}Date: {{transaction_date}}If you have any questions about this transaction, please contact our support team.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences.","email_body":"Balance Deducted\r\n\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Balance Deducteded\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            A deduction has been made from your account balance.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},A deduction has been made from your account balance as requested.Amount Deducted: {{amount}} {{currency}}Remaining Balance: {{new_balance}} {{currency}}Transaction ID: {{transaction_id}}Date: {{transaction_date}}If you have any questions about this transaction, please contact our support team.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences.","template_id":"2","sms_status":"on","sms_body":"{{amount}} {{wallet_currency}}  debited from your account. Your Current Balance {{post_balance}} {{wallet_currency}} . Transaction: #{{trx}}. Admin Note is {{remark}}"}  
[2025-06-30 18:18:06] production.INFO: Request Headers: {"content-type":["application\/x-www-form-urlencoded"],"content-length":["13511"],"x-original-url":["\/admin\/notification\/template\/update\/2"],"priority":["u=0, i"],"sec-fetch-dest":["document"],"sec-fetch-user":["?1"],"sec-fetch-mode":["navigate"],"sec-fetch-site":["same-origin"],"upgrade-insecure-requests":["1"],"origin":["https:\/\/mbf.mybrokerforex.com"],"sec-ch-ua-platform":["\"Windows\""],"sec-ch-ua-mobile":["?0"],"sec-ch-ua":["\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not\/A)Brand\";v=\"24\""],"user-agent":["Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36"],"referer":["https:\/\/mbf.mybrokerforex.com\/admin\/notification\/template\/edit\/2"],"host":["mbf.mybrokerforex.com"],"cookie":["PHPSESSID=d3952eed09dd37b5504eefbe7d77664a; XSRF-TOKEN=eyJpdiI6IjJscE1VK1R1bHQ0TnVBZ21lUG5KN0E9PSIsInZhbHVlIjoiWHdhVEJDR3lMOUxKS2pOdGRIOEtKbWZxeEozUDFjRjRSeVJzU2tXVFJPbzN1RDh5UklBOGgwN1IzSC95WTI2dVhobWdBRGh6VEoyYUR5alRPcWdlNmhtVE5vNmZZR3lRZE5UUWN3NTFlS0hjWmxqOURYQnNvS0owNUZkMVR3a28iLCJtYWMiOiI2MmRkOTJlNzE3ODA5YmEwZDkzNmQzZGJjNjcwYjVjMjM0YTQ3OGE1OWY1NTlmMDAwMzY4Y2I5N2U1NzVjZDhlIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InZSbGpSQWVkb1MvUTgya1VEbkpIQ1E9PSIsInZhbHVlIjoicWRmdVY0UVAxRjNaWERSUG91TC81NGNxUFJIQUhmbnBacGV0dTV1cE5ja1ZWU3ZkVm52SnE5emg0aThaK1JnTVo2WXFlbTBDTjVMbWtmT3czb0lJMXFkME1MRTBtdi81ZzlGOTJCNXJLeDY0S2tBSGx0NmlBNjJKOEp4NzRqUGkiLCJtYWMiOiJiMDYyOTdhNmQ4ZDVhYmJhNjZkYzg5YjljOWM4OWVlNzExZWU3YmI5MmFmZGI3Y2FhNDM0NzRiMWJmZGEwZjA4IiwidGFnIjoiIn0%3D"],"accept-language":["en-US,en;q=0.9,ms-MY;q=0.8,ms;q=0.7,zh-CN;q=0.6,zh;q=0.5"],"accept-encoding":["gzip, deflate, br, zstd"],"accept":["text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7"],"connection":["close"],"cache-control":["max-age=0"]}  
[2025-06-30 18:18:06] production.INFO: ✅ Validation passed  
[2025-06-30 18:18:06] production.INFO: ✅ Template found - Current subject: Your Account has been Debited  
[2025-06-30 18:18:06] production.INFO: ✅ Template found - Current email_body length: 3743  
[2025-06-30 18:18:06] production.INFO: ✅ Subject updated to: Your Account has been Debited  
[2025-06-30 18:18:06] production.INFO: === SIMPLIFIED EMAIL BODY PROCESSING ===  
[2025-06-30 18:18:06] production.INFO: Email body source: email_body_final  
[2025-06-30 18:18:06] production.INFO: Email body length: 1639  
[2025-06-30 18:18:06] production.INFO: Email body preview (first 200 chars): Balance Deducted


    
    
        
            
                
                

                    
                    
                        
                            
      
[2025-06-30 18:18:06] production.INFO: Template 2: Using content directly from editor - NO PROCESSING  
[2025-06-30 18:18:06] production.INFO: === FINAL CONTENT READY FOR SAVE ===  
[2025-06-30 18:18:06] production.INFO: Final email body length: 1639  
[2025-06-30 18:18:06] production.INFO: Final email body preview (first 300 chars): Balance Deducted


    
    
        
            
                
                

                    
                    
                        
                            
                        
                    

                    
                    
            
[2025-06-30 18:18:06] production.INFO: Template 2: Skipping all corruption detection and complex processing to prevent issues  
[2025-06-30 18:18:06] production.INFO: === DATABASE SAVE OPERATION DEBUG ===  
[2025-06-30 18:18:06] production.INFO: Before save - Template email_body length: 3743  
[2025-06-30 18:18:06] production.INFO: Before save - New email_body length: 1639  
[2025-06-30 18:18:06] production.INFO: Before save - Template dirty: []  
[2025-06-30 18:18:06] production.INFO: After setting fields - Template dirty: {"email_body":"Balance Deducted\r\n\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Balance Deducteded\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            A deduction has been made from your account balance.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},A deduction has been made from your account balance as requested.Amount Deducted: {{amount}} {{currency}}Remaining Balance: {{new_balance}} {{currency}}Transaction ID: {{transaction_id}}Date: {{transaction_date}}If you have any questions about this transaction, please contact our support team.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences."}  
[2025-06-30 18:18:06] production.INFO: After setting fields - Template email_body length: 1639  
[2025-06-30 18:18:06] production.INFO: Save operation result: SUCCESS  
[2025-06-30 18:18:06] production.INFO: After refresh - Template email_body length: 1639  
[2025-06-30 18:18:06] production.INFO: After refresh - Content matches: YES  
[2025-06-30 18:18:06] production.INFO: ✅ Template 2: Database operation completed  
[2025-06-30 18:18:06] production.INFO: === TEMPLATE UPDATE DEBUG END ===  
[2025-06-30 18:18:32] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:18:32] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:18:49] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:18:49] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:18:52] production.INFO: === TEMPLATE UPDATE DEBUG START ===  
[2025-06-30 18:18:52] production.INFO: Template ID: 36  
[2025-06-30 18:18:52] production.INFO: Request Method: POST  
[2025-06-30 18:18:52] production.INFO: Request URL: https://mbf.mybrokerforex.com/admin/notification/template/update/36  
[2025-06-30 18:18:52] production.INFO: User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-06-30 18:18:52] production.INFO: All Request Data: {"_token":"s7zCWLvR4ufbAuzWCrOwMAp2FwKDbsLrtEnT6KmN","subject":"IB Application Status Update","email_status":"on","email_body_final":"IB Application Rejected\r\n\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            IB Application Rejectede\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            We have reviewed your IB application.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},We have reviewed your IB application and unfortunately cannot approve it at this time.Application Date: {{application_date}}IB Type: {{requested_ib_type}}Review Date: {{review_date}}Rejection Reason: {{rejection_reason}}You may reapply after addressing the mentioned requirements.Thank you for your interest in our IB program.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences.","email_body":"IB Application Rejected\r\n\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            IB Application Rejectede\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            We have reviewed your IB application.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},We have reviewed your IB application and unfortunately cannot approve it at this time.Application Date: {{application_date}}IB Type: {{requested_ib_type}}Review Date: {{review_date}}Rejection Reason: {{rejection_reason}}You may reapply after addressing the mentioned requirements.Thank you for your interest in our IB program.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences.","template_id":"36","sms_body":"Your IB application has been rejected. Reason: {{reason}}"}  
[2025-06-30 18:18:52] production.INFO: Request Headers: {"content-type":["application\/x-www-form-urlencoded"],"content-length":["13596"],"x-original-url":["\/admin\/notification\/template\/update\/36"],"priority":["u=0, i"],"sec-fetch-dest":["document"],"sec-fetch-user":["?1"],"sec-fetch-mode":["navigate"],"sec-fetch-site":["same-origin"],"upgrade-insecure-requests":["1"],"origin":["https:\/\/mbf.mybrokerforex.com"],"sec-ch-ua-platform":["\"Windows\""],"sec-ch-ua-mobile":["?0"],"sec-ch-ua":["\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not\/A)Brand\";v=\"24\""],"user-agent":["Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36"],"referer":["https:\/\/mbf.mybrokerforex.com\/admin\/notification\/template\/edit\/36"],"host":["mbf.mybrokerforex.com"],"cookie":["XSRF-TOKEN=eyJpdiI6ImpRSFZ3WXhpekNiS0lkQkNsN3UySmc9PSIsInZhbHVlIjoiT2pVd0xSS0YwZjJVbVkraGNuTEtkb0tzR0toWXcrWi9KSnYyVUZPZ1NFdGJ5b0x6ck43NU9iMTN6dFk2OFlodjVub1RoOExPZXh5WCthMlM5ajhFTFBjbWtNQmpRUXdLSEJ1ZDNPSlZLb0MySWRiZGdsaFFVUHN0V2xYT2wrR28iLCJtYWMiOiIwZTRhMWQwOTZmYzRhODhhMTkxMDlmOTUxOTgxNDA0Yjc1N2Q5NTUxZGY4YjljNWZlNzY4YjFlMWNmYzU5OGNlIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImhoTlF6UlVuaUtMck50NnN3U0N0NXc9PSIsInZhbHVlIjoibi8yNk9CdnYwN2RvRmhXVE5JN0h2YWF4TCs4bnZoVTBvRzZmY0JhSms0VGJ1TEhXRHlKSTQwdlZ5M1A1aER5OEhhWWhJUW5hL1c2dkFZM0NwUU9QeHQ5Y09VcmlDZ2V6NlUySGxlL2lvUXVxbnVyeVZvSEZtVm93clpqTFJLRDQiLCJtYWMiOiJmM2JhMjEyOTc1NTVhYTQ0MTNmYWQyNzFjYzllZDY0MGFlZjk3MDFmMTFmMGM0OTg5YWI0MzA1MDZjMzY5NGU1IiwidGFnIjoiIn0%3D"],"accept-language":["en-US,en;q=0.9"],"accept-encoding":["gzip, deflate, br, zstd"],"accept":["text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7"],"connection":["close"],"cache-control":["max-age=0"]}  
[2025-06-30 18:18:52] production.INFO: ✅ Validation passed  
[2025-06-30 18:18:52] production.INFO: ✅ Template found - Current subject: IB Application Status Update  
[2025-06-30 18:18:52] production.INFO: ✅ Template found - Current email_body length: 3781  
[2025-06-30 18:18:52] production.INFO: ✅ Subject updated to: IB Application Status Update  
[2025-06-30 18:18:52] production.INFO: === SIMPLIFIED EMAIL BODY PROCESSING ===  
[2025-06-30 18:18:52] production.INFO: Email body source: email_body_final  
[2025-06-30 18:18:52] production.INFO: Email body length: 1669  
[2025-06-30 18:18:52] production.INFO: Email body preview (first 200 chars): IB Application Rejected


    
    
        
            
                
                

                    
                    
                        
                             
[2025-06-30 18:18:52] production.INFO: Template 36: Using content directly from editor - NO PROCESSING  
[2025-06-30 18:18:52] production.INFO: === FINAL CONTENT READY FOR SAVE ===  
[2025-06-30 18:18:52] production.INFO: Final email body length: 1669  
[2025-06-30 18:18:52] production.INFO: Final email body preview (first 300 chars): IB Application Rejected


    
    
        
            
                
                

                    
                    
                        
                            
                        
                    

                    
                    
     
[2025-06-30 18:18:52] production.INFO: Template 36: Skipping all corruption detection and complex processing to prevent issues  
[2025-06-30 18:18:52] production.INFO: === DATABASE SAVE OPERATION DEBUG ===  
[2025-06-30 18:18:52] production.INFO: Before save - Template email_body length: 3781  
[2025-06-30 18:18:52] production.INFO: Before save - New email_body length: 1669  
[2025-06-30 18:18:52] production.INFO: Before save - Template dirty: []  
[2025-06-30 18:18:52] production.INFO: After setting fields - Template dirty: {"email_body":"IB Application Rejected\r\n\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            IB Application Rejectede\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            We have reviewed your IB application.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},We have reviewed your IB application and unfortunately cannot approve it at this time.Application Date: {{application_date}}IB Type: {{requested_ib_type}}Review Date: {{review_date}}Rejection Reason: {{rejection_reason}}You may reapply after addressing the mentioned requirements.Thank you for your interest in our IB program.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences."}  
[2025-06-30 18:18:52] production.INFO: After setting fields - Template email_body length: 1669  
[2025-06-30 18:18:52] production.INFO: Save operation result: SUCCESS  
[2025-06-30 18:18:52] production.INFO: After refresh - Template email_body length: 1669  
[2025-06-30 18:18:52] production.INFO: After refresh - Content matches: YES  
[2025-06-30 18:18:52] production.INFO: ✅ Template 36: Database operation completed  
[2025-06-30 18:18:52] production.INFO: === TEMPLATE UPDATE DEBUG END ===  
[2025-06-30 18:19:19] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:19:19] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:19:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:19:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:19:50] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:19:50] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:20:20] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:20:20] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:20:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:20:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:20:50] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:20:50] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:21:20] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:21:20] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:21:30] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:21:30] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:22:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:22:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:22:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:22:57] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:22:57] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:23:04] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:23:04] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:23:14] production.INFO: === TEMPLATE UPDATE DEBUG START ===  
[2025-06-30 18:23:14] production.INFO: Template ID: 33  
[2025-06-30 18:23:14] production.INFO: Request Method: POST  
[2025-06-30 18:23:14] production.INFO: Request URL: https://mbf.mybrokerforex.com/admin/notification/template/update/33  
[2025-06-30 18:23:14] production.INFO: User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-06-30 18:23:14] production.INFO: All Request Data: {"_token":"WbSY2xiYrIOaKJx5dwGbkjlIuchP7ZKjbaOKfCIn","subject":"New KYC Document Submitted - {{username}}","email_status":"on","email_body_final":"Account Notification\r\n\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Account Notifications\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            This is an important notification regarding your account.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},This is an important notification regarding your account.{{message}}If you have any questions, please contact our support team.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences.","email_body":"Account Notification\r\n\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Account Notifications\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            This is an important notification regarding your account.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},This is an important notification regarding your account.{{message}}If you have any questions, please contact our support team.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences.","template_id":"33","sms_body":"New KYC submission from {{username}}. Please review in admin panel."}  
[2025-06-30 18:23:14] production.INFO: Request Headers: {"content-type":["application\/x-www-form-urlencoded"],"content-length":["12789"],"x-original-url":["\/admin\/notification\/template\/update\/33"],"priority":["u=0, i"],"sec-fetch-dest":["document"],"sec-fetch-user":["?1"],"sec-fetch-mode":["navigate"],"sec-fetch-site":["same-origin"],"upgrade-insecure-requests":["1"],"origin":["https:\/\/mbf.mybrokerforex.com"],"sec-ch-ua-platform":["\"Windows\""],"sec-ch-ua-mobile":["?0"],"sec-ch-ua":["\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not\/A)Brand\";v=\"24\""],"user-agent":["Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36"],"referer":["https:\/\/mbf.mybrokerforex.com\/admin\/notification\/template\/edit\/33"],"host":["mbf.mybrokerforex.com"],"cookie":["PHPSESSID=d3952eed09dd37b5504eefbe7d77664a; XSRF-TOKEN=eyJpdiI6IllLQWZwMmxnSEJwNCtpK21nbE1kUFE9PSIsInZhbHVlIjoicHBlSkREOUtQS1pRendtQzkyNnhoUjROQmQ1Uzd6YmN0Snl4by9HMlRYdjVKSEQ1VXNLcitrcWovUXd1bjMyUzkzUnV1VzF1LzRSMkc1Q01IUVE0SnM5cXNxa2VlMDBMdGlGeDVUdEUzUFlDR2IzMWlWWi9HUU9VcE9nOHppaGQiLCJtYWMiOiJmZWVkODY1MWM1M2Q2YTQ4ZGRmMjAzMmRkNmE0YzMwZDUwZjYxZjU4MTE2YTJlZWVhYmQ2YTRkZjkzNWQ1YmVmIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InhqNFFWVGlySVJrQ3RNOG9zR0ZNVlE9PSIsInZhbHVlIjoiK3R1a0NlZEVWZHFPN21Bbng1ZThlR0d2ZXFnMFVzdXhzSkhGZW1hTWJGY1FOUStLcG1hckhwQTZrRUVCbGh2UzJObmNNQ0RMb2NxZGNEU3FiUkNsaGo3UXNyN2VjbjcvOHhIbmZxZFpWaGUrZTBNZWE1UGEzVjRYOTJQSE1mRjUiLCJtYWMiOiJjZDNmNDcwNjI2M2Q2MmU3YTJmY2Q4Y2JkZDQ2ZGE5ZDM0OTEzNDk5OWZhZGJiOTVhMzc4MWJlMWYzYzVhNDY5IiwidGFnIjoiIn0%3D"],"accept-language":["en-US,en;q=0.9,ms-MY;q=0.8,ms;q=0.7,zh-CN;q=0.6,zh;q=0.5"],"accept-encoding":["gzip, deflate, br, zstd"],"accept":["text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7"],"connection":["close"],"cache-control":["max-age=0"]}  
[2025-06-30 18:23:14] production.INFO: ✅ Validation passed  
[2025-06-30 18:23:14] production.INFO: ✅ Template found - Current subject: New KYC Document Submitted - {{username}}  
[2025-06-30 18:23:14] production.INFO: ✅ Template found - Current email_body length: 3483  
[2025-06-30 18:23:14] production.INFO: ✅ Subject updated to: New KYC Document Submitted - {{username}}  
[2025-06-30 18:23:14] production.INFO: === SIMPLIFIED EMAIL BODY PROCESSING ===  
[2025-06-30 18:23:14] production.INFO: Email body source: email_body_final  
[2025-06-30 18:23:14] production.INFO: Email body length: 1484  
[2025-06-30 18:23:14] production.INFO: Email body preview (first 200 chars): Account Notification


    
    
        
            
                
                

                    
                    
                        
                            
  
[2025-06-30 18:23:14] production.INFO: Template 33: Using content directly from editor - NO PROCESSING  
[2025-06-30 18:23:14] production.INFO: === FINAL CONTENT READY FOR SAVE ===  
[2025-06-30 18:23:14] production.INFO: Final email body length: 1484  
[2025-06-30 18:23:14] production.INFO: Final email body preview (first 300 chars): Account Notification


    
    
        
            
                
                

                    
                    
                        
                            
                        
                    

                    
                    
        
[2025-06-30 18:23:14] production.INFO: Template 33: Skipping all corruption detection and complex processing to prevent issues  
[2025-06-30 18:23:14] production.INFO: === DATABASE SAVE OPERATION DEBUG ===  
[2025-06-30 18:23:14] production.INFO: Before save - Template email_body length: 3483  
[2025-06-30 18:23:14] production.INFO: Before save - New email_body length: 1484  
[2025-06-30 18:23:14] production.INFO: Before save - Template dirty: []  
[2025-06-30 18:23:14] production.INFO: After setting fields - Template dirty: {"email_body":"Account Notification\r\n\r\n\r\n    \r\n    \r\n        \r\n            \r\n                \r\n                \r\n\r\n                    \r\n                    \r\n                        \r\n                            \r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Account Notifications\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            This is an important notification regarding your account.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Dear {{fullname}},This is an important notification regarding your account.{{message}}If you have any questions, please contact our support team.\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            Best regards,MBFX Team\r\n                        \r\n                    \r\n\r\n                    \r\n                    \r\n                        \r\n                            MBFX - Professional Trading Platform\r\n                            Account Settings | Contact Support | Privacy Policy\r\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences."}  
[2025-06-30 18:23:14] production.INFO: After setting fields - Template email_body length: 1484  
[2025-06-30 18:23:14] production.INFO: Save operation result: SUCCESS  
[2025-06-30 18:23:14] production.INFO: After refresh - Template email_body length: 1484  
[2025-06-30 18:23:14] production.INFO: After refresh - Content matches: YES  
[2025-06-30 18:23:14] production.INFO: ✅ Template 33: Database operation completed  
[2025-06-30 18:23:14] production.INFO: === TEMPLATE UPDATE DEBUG END ===  
[2025-06-30 18:23:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:23:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:23:32] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:23:32] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:24:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:24:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:24:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:24:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:25:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:25:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:25:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:25:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:26:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:26:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:26:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:27:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:27:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:27:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:28:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:28:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:29:30] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:29:30] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:29:30] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:30:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:30:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:30:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:31:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:31:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:32:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:32:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:32:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:32:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:33:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:33:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:33:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:34:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:34:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:35:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:35:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:35:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:35:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:36:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:36:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:37:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:37:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:38:08] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:38:08] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:38:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:38:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:38:32] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:38:32] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:39:04] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:39:04] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:39:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:39:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:40:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:40:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:40:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:40:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:41:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:41:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:41:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:41:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:42:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:42:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:42:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:43:31] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:43:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:43:31] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:43:38] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:43:38] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:43:40] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:43:40] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:43:53] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:43:53] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:44:12] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:44:12] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:44:12] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:44:36] production.INFO: === TEMPLATE UPDATE DEBUG START ===  
[2025-06-30 18:44:36] production.INFO: Template ID: 4  
[2025-06-30 18:44:36] production.INFO: Request Method: POST  
[2025-06-30 18:44:36] production.INFO: Request URL: https://mbf.mybrokerforex.com/admin/notification/template/update/4  
[2025-06-30 18:44:36] production.INFO: User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-06-30 18:44:36] production.INFO: Server Environment: WINNT - PHP 8.4.8  
[2025-06-30 18:44:36] production.INFO: Content Type: application/x-www-form-urlencoded  
[2025-06-30 18:44:36] production.INFO: Content Length: 13570  
[2025-06-30 18:44:36] production.INFO: Raw POST data keys: _token, subject, email_status, email_body_final, email_body, template_id, sms_status, sms_body  
[2025-06-30 18:44:36] production.INFO: Email body field exists: YES  
[2025-06-30 18:44:36] production.INFO: Email body final field exists: YES  
[2025-06-30 18:44:36] production.INFO: ✅ Validation passed  
[2025-06-30 18:44:36] production.INFO: ✅ Template found - Current subject: Your Deposit is Approved  
[2025-06-30 18:44:36] production.INFO: ✅ Template found - Current email_body length: 3788  
[2025-06-30 18:44:36] production.INFO: ✅ Subject updated to: Your Deposit is Approved  
[2025-06-30 18:44:36] production.INFO: === WINDOWS SERVER SPECIFIC EMAIL BODY PROCESSING ===  
[2025-06-30 18:44:36] production.INFO: Method 1 - email_body_final found, length: 1656  
[2025-06-30 18:44:36] production.INFO: Method 1 - email_body found, length: 1656  
[2025-06-30 18:44:36] production.INFO: Method 2 - email_body_final in POST data, length: 1656  
[2025-06-30 18:44:36] production.INFO: Method 2 - email_body in POST data, length: 1656  
[2025-06-30 18:44:36] production.INFO: Email body source: email_body_final  
[2025-06-30 18:44:36] production.INFO: Email body length: 1656  
[2025-06-30 18:44:36] production.INFO: After Windows cleanup - Email body length: 1606  
[2025-06-30 18:44:36] production.INFO: Email body preview (first 200 chars): Deposit Approved


    
    
        
            
                
                

                    
                    
                        
                            
                    
[2025-06-30 18:44:36] production.INFO: Template 4: Using cleaned content directly - NO FURTHER PROCESSING  
[2025-06-30 18:44:36] production.INFO: === FINAL CONTENT READY FOR SAVE ===  
[2025-06-30 18:44:36] production.INFO: Final email body length: 1606  
[2025-06-30 18:44:36] production.INFO: Final email body preview (first 300 chars): Deposit Approved


    
    
        
            
                
                

                    
                    
                        
                            
                        
                    

                    
                    
                        
      
[2025-06-30 18:44:36] production.INFO: Template 4: Skipping all corruption detection and complex processing to prevent issues  
[2025-06-30 18:44:36] production.INFO: === DATABASE SAVE OPERATION DEBUG ===  
[2025-06-30 18:44:36] production.INFO: Before save - Template email_body length: 3788  
[2025-06-30 18:44:36] production.INFO: Before save - New email_body length: 1606  
[2025-06-30 18:44:36] production.INFO: Before save - Template dirty: []  
[2025-06-30 18:44:36] production.INFO: After setting fields - Template dirty: {"email_body":"Deposit Approved\n\n\n    \n    \n        \n            \n                \n                \n\n                    \n                    \n                        \n                            \n                        \n                    \n\n                    \n                    \n                        \n                            Deposit Approved\n                        \n                    \n\n                    \n                    \n                        \n                            Your deposit request has been approved and processed.\n                        \n                    \n\n                    \n                    \n                        \n                            Dear {{fullname}},Great news! Your deposit request has been approved and processed successfully.Approved Amount: {{amount}} {{currency}}Payment Method: {{method_name}}Transaction ID: {{trx}}Approval Date: {{approval_date}}Updated Balance: {{post_balance}} {{currency}}The funds are now available in your account for immediate use.\n                        \n                    \n\n                    \n                    \n                        \n                            Best regards,MBFX Team\n                        \n                    \n\n                    \n                    \n                        \n                            MBFX - Professional Trading Platform\n                            Account Settings | Contact Support | Privacy Policy\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences."}  
[2025-06-30 18:44:36] production.INFO: After setting fields - Template email_body length: 1606  
[2025-06-30 18:44:36] production.INFO: Save operation result: SUCCESS  
[2025-06-30 18:44:36] production.INFO: After refresh - Template email_body length: 1606  
[2025-06-30 18:44:36] production.INFO: After refresh - Content matches: YES  
[2025-06-30 18:44:36] production.INFO: ✅ Template 4: Database operation completed  
[2025-06-30 18:44:36] production.INFO: === TEMPLATE UPDATE DEBUG END ===  
[2025-06-30 18:44:43] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:44:43] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:45:13] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:45:13] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:45:42] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:45:42] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:46:13] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:46:13] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 18:46:42] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 18:46:42] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
